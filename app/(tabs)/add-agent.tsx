import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import React, { useEffect, useRef, useState } from 'react';
import {
    Animated,
    Dimensions,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/context/AuthContext';

const { width, height } = Dimensions.get('window');

type InputFieldProps = {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder: string;
  icon: string;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  secureTextEntry?: boolean;
  multiline?: boolean;
  numberOfLines?: number;
};

const InputField = ({
  label,
  value,
  onChangeText,
  placeholder,
  icon,
  keyboardType = 'default',
  autoCapitalize = 'sentences',
  secureTextEntry = false,
  multiline = false,
  numberOfLines = 1
}: InputFieldProps) => {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <View style={styles.inputContainer}>
      <View style={styles.labelContainer}>
        <IconSymbol size={18} name={icon} color={Colors.light.primary} />
        <ThemedText style={styles.inputLabel}>{label}</ThemedText>
      </View>
      <TextInput
        style={[
          styles.input,
          isFocused && styles.inputFocused,
          value ? styles.inputFilled : {},
          multiline && { height: 24 * numberOfLines, textAlignVertical: 'top', paddingTop: 12 }
        ]}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={Colors.light.placeholder}
        keyboardType={keyboardType}
        autoCapitalize={autoCapitalize}
        secureTextEntry={secureTextEntry}
        multiline={multiline}
        numberOfLines={numberOfLines}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
      />
    </View>
  );
};

export default function AddAgentScreen() {
  const { user } = useAuth();
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [pollingStation, setPollingStation] = useState('');
  const [notes, setNotes] = useState('');

  // Check if user is Hon or manager, if not redirect to dashboard
  useEffect(() => {
    if (user?.role !== 'hon' && user?.role !== 'manager') {
      router.replace('/(tabs)');
    }
  }, [user]);

  // Animation for button press
  const animatedButtonScale = useRef(new Animated.Value(1)).current;

  const onPressIn = () => {
    Animated.spring(animatedButtonScale, {
      toValue: 0.96,
      useNativeDriver: true,
    }).start();
  };

  const onPressOut = () => {
    Animated.spring(animatedButtonScale, {
      toValue: 1,
      friction: 4,
      tension: 40,
      useNativeDriver: true,
    }).start();
  };

  const handleAddAgent = () => {
    // TODO: Implement actual agent addition API call
    console.log('Adding agent:', { name, phone, email, pollingStation, notes });

    // Show success message and navigate back to dashboard
    router.back();
  };

  const isFormValid = name && phone && pollingStation;

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardView}
    >
      <ThemedView style={styles.container}>
        {/* Header */}
        <LinearGradient
          colors={[Colors.light.primary, Colors.light.secondary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <IconSymbol size={24} name="chevron.left" color="white" />
            </TouchableOpacity>
            <ThemedText style={styles.headerTitle}>Add New Agent</ThemedText>
            <View style={{ width: 24 }} />
          </View>
        </LinearGradient>

        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          <View style={styles.formContainer}>
            <ThemedText style={styles.formTitle}>Agent Information</ThemedText>
            <ThemedText style={styles.formSubtitle}>
              Add a new agent to help manage your campaign
            </ThemedText>

            <View style={styles.card}>
              <InputField
                label="Full Name"
                value={name}
                onChangeText={setName}
                placeholder="Enter agent's full name"
                icon="person.fill"
              />

              <InputField
                label="Phone Number"
                value={phone}
                onChangeText={setPhone}
                placeholder="Enter agent's phone number"
                icon="phone.fill"
                keyboardType="phone-pad"
              />

              <InputField
                label="Email Address (Optional)"
                value={email}
                onChangeText={setEmail}
                placeholder="Enter agent's email address"
                icon="envelope.fill"
                keyboardType="email-address"
                autoCapitalize="none"
              />

              <InputField
                label="Polling Station"
                value={pollingStation}
                onChangeText={setPollingStation}
                placeholder="Assign a polling station"
                icon="building.2.fill"
              />

              <InputField
                label="Notes (Optional)"
                value={notes}
                onChangeText={setNotes}
                placeholder="Add any additional notes about this agent"
                icon="note.text"
                multiline={true}
                numberOfLines={4}
              />

              <Animated.View
                style={[
                  styles.buttonContainer,
                  { transform: [{ scale: animatedButtonScale }] }
                ]}
              >
                <TouchableOpacity
                  style={[
                    styles.button,
                    !isFormValid && styles.buttonDisabled
                  ]}
                  onPress={handleAddAgent}
                  disabled={!isFormValid}
                  activeOpacity={0.9}
                  onPressIn={onPressIn}
                  onPressOut={onPressOut}
                >
                  <LinearGradient
                    colors={[Colors.light.primary, Colors.light.secondary]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.buttonGradient}
                  >
                    <IconSymbol size={20} name="person.fill.badge.plus" color="white" style={styles.buttonIcon} />
                    <ThemedText style={styles.buttonText}>Add Agent</ThemedText>
                  </LinearGradient>
                </TouchableOpacity>
              </Animated.View>
            </View>
          </View>
        </ScrollView>
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  keyboardView: {
    flex: 1,
    backgroundColor: Colors.light.neutral[50],
  },
  container: {
    flex: 1,
    backgroundColor: Colors.light.neutral[50],
  },
  header: {
    height: 110,
    paddingTop: 50,
    paddingHorizontal: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 40,
  },
  formContainer: {
    padding: 20,
  },
  formTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.neutral[800],
    marginBottom: 8,
  },
  formSubtitle: {
    fontSize: 16,
    color: Colors.light.neutral[500],
    marginBottom: 24,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  inputContainer: {
    marginBottom: 20,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.neutral[700],
    marginLeft: 8,
  },
  input: {
    height: 50,
    borderWidth: 1.5,
    borderColor: Colors.light.neutral[200],
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: Colors.light.neutral[50],
    color: Colors.light.neutral[800],
  },
  inputFocused: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.neutral[50],
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 2,
  },
  inputFilled: {
    borderColor: Colors.light.neutral[300],
    backgroundColor: Colors.light.neutral[50],
  },
  buttonContainer: {
    marginTop: 10,
  },
  button: {
    height: 56,
    borderRadius: 12,
    overflow: 'hidden',
  },
  buttonGradient: {
    height: '100%',
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
});