import { LinearGradient } from 'expo-linear-gradient';
import { Dimensions, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

import ScreenWrapper from '@/components/ScreenWrapper';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/context/AuthContext';

const { width } = Dimensions.get('window');

export default function ProfileScreen() {
  const { user, logout } = useAuth();

  // Determine user role
  const isAgent = user?.role === 'agent';

  // Get role display text
  const getRoleDisplay = () => {
    switch (user?.role) {
      case 'hon':
        return 'Honorable Member';
      case 'manager':
        return 'Campaign Manager';
      case 'agent':
        return 'Polling Agent';
      default:
        return 'User';
    }
  };

  // Get role badge color
  const getRoleBadgeColor = () => {
    switch (user?.role) {
      case 'hon':
        return Colors.light.primary;
      case 'manager':
        return '#10B981'; // Green
      case 'agent':
        return '#F59E0B'; // Amber
      default:
        return Colors.light.neutral[500];
    }
  };

  const handleLogout = async () => {
    console.log('Profile: Direct logout initiated');
    try {
      await logout();
      console.log('Profile: Direct logout completed');
    } catch (error) {
      console.error('Profile: Direct logout failed:', error);
    }
  };

  return (
    <ScreenWrapper backgroundColor={Colors.light.neutral[50]}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Header with gradient background */}
        <LinearGradient
          colors={[Colors.light.primary, Colors.light.secondary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <View style={styles.avatarContainer}>
              <ThemedText style={styles.avatarText}>
                {user?.name.split(' ').map(n => n[0]).join('')}
              </ThemedText>
            </View>
            <ThemedText style={styles.userName}>{user?.name}</ThemedText>
            <View style={[styles.roleBadge, { backgroundColor: getRoleBadgeColor() }]}>
              <ThemedText style={styles.roleText}>{getRoleDisplay()}</ThemedText>
            </View>
          </View>
        </LinearGradient>

        <ThemedView style={styles.container}>
          {/* Rest of the profile content */}
          {/* User Info Card */}
          <ThemedView style={styles.infoCard}>
            <View style={styles.infoRow}>
              <View style={styles.infoIconContainer}>
                <IconSymbol size={20} name="phone.fill" color={Colors.light.primary} />
              </View>
              <View style={styles.infoTextContainer}>
                <ThemedText style={styles.infoLabel}>Phone Number</ThemedText>
                <ThemedText style={styles.infoValue}>{user?.phone || 'Not provided'}</ThemedText>
              </View>
            </View>

            {user?.station && (
              <View style={styles.infoRow}>
                <View style={styles.infoIconContainer}>
                  <IconSymbol size={20} name="mappin.and.ellipse" color={Colors.light.primary} />
                </View>
                <View style={styles.infoTextContainer}>
                  <ThemedText style={styles.infoLabel}>Polling Station</ThemedText>
                  <ThemedText style={styles.infoValue}>{user.station}</ThemedText>
                </View>
              </View>
            )}

            <View style={styles.infoRow}>
              <View style={styles.infoIconContainer}>
                <IconSymbol size={20} name="calendar" color={Colors.light.primary} />
              </View>
              <View style={styles.infoTextContainer}>
                <ThemedText style={styles.infoLabel}>Joined</ThemedText>
                <ThemedText style={styles.infoValue}>January 15, 2023</ThemedText>
              </View>
            </View>
          </ThemedView>

          {/* Settings Section - Hidden for agents */}
          {!isAgent && (
            <>
              <ThemedText style={styles.sectionTitle}>Settings</ThemedText>
              <ThemedView style={styles.menuSection}>
                <TouchableOpacity style={styles.menuItem}>
                  <View style={[styles.menuIconContainer, { backgroundColor: `${Colors.light.primary}15` }]}>
                    <IconSymbol size={20} name="person.fill" color={Colors.light.primary} />
                  </View>
                  <ThemedText style={styles.menuText}>Edit Profile</ThemedText>
                  <IconSymbol size={16} name="chevron.right" color={Colors.light.neutral[400]} style={styles.chevron} />
                </TouchableOpacity>

                <TouchableOpacity style={styles.menuItem}>
                  <View style={[styles.menuIconContainer, { backgroundColor: '#10B98115' }]}>
                    <IconSymbol size={20} name="bell.fill" color="#10B981" />
                  </View>
                  <ThemedText style={styles.menuText}>Notifications</ThemedText>
                  <IconSymbol size={16} name="chevron.right" color={Colors.light.neutral[400]} style={styles.chevron} />
                </TouchableOpacity>

                <TouchableOpacity style={styles.menuItem}>
                  <View style={[styles.menuIconContainer, { backgroundColor: '#3B82F615' }]}>
                    <IconSymbol size={20} name="lock.fill" color="#3B82F6" />
                  </View>
                  <ThemedText style={styles.menuText}>Change Password</ThemedText>
                  <IconSymbol size={16} name="chevron.right" color={Colors.light.neutral[400]} style={styles.chevron} />
                </TouchableOpacity>
              </ThemedView>
            </>
          )}

          {/* Support Section */}
          <ThemedText style={styles.sectionTitle}>Support</ThemedText>
          <ThemedView style={styles.menuSection}>
            <TouchableOpacity style={styles.menuItem}>
              <View style={[styles.menuIconContainer, { backgroundColor: '#F59E0B15' }]}>
                <IconSymbol size={20} name="questionmark.circle.fill" color="#F59E0B" />
              </View>
              <ThemedText style={styles.menuText}>Help & Support</ThemedText>
              <IconSymbol size={16} name="chevron.right" color={Colors.light.neutral[400]} style={styles.chevron} />
            </TouchableOpacity>

            <TouchableOpacity style={styles.menuItem}>
              <View style={[styles.menuIconContainer, { backgroundColor: '#8B5CF615' }]}>
                <IconSymbol size={20} name="info.circle.fill" color="#8B5CF6" />
              </View>
              <ThemedText style={styles.menuText}>About Mitally</ThemedText>
              <IconSymbol size={16} name="chevron.right" color={Colors.light.neutral[400]} style={styles.chevron} />
            </TouchableOpacity>
          </ThemedView>

          {/* Logout Button */}
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <View style={styles.logoutContent}>
              <View style={[styles.logoutIconContainer, { backgroundColor: `${Colors.light.error}15` }]}>
                <IconSymbol size={20} name="arrow.right.square.fill" color={Colors.light.error} />
              </View>
              <ThemedText style={styles.logoutText}>Logout</ThemedText>
            </View>
          </TouchableOpacity>

          <ThemedText style={styles.versionText}>Mitally v1.0.0</ThemedText>
        </ThemedView>
      </ScrollView>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: Colors.light.neutral[50],
  },
  container: {
    flex: 1,
    padding: 20,
    paddingTop: 0,
    backgroundColor: Colors.light.neutral[50],
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    alignItems: 'center',
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  avatarText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: 'white',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  roleBadge: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  roleText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  infoCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginTop: -20,
    marginBottom: 24,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  infoIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${Colors.light.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  infoTextContainer: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: Colors.light.neutral[500],
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.neutral[800],
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.neutral[800],
    marginBottom: 16,
    marginLeft: 4,
  },
  menuSection: {
    backgroundColor: 'white',
    borderRadius: 16,
    marginBottom: 24,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    overflow: 'hidden',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.neutral[100],
  },
  menuIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  menuText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.neutral[800],
  },
  chevron: {
    marginLeft: 'auto',
  },
  logoutButton: {
    backgroundColor: 'white',
    borderRadius: 16,
    marginBottom: 24,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    overflow: 'hidden',
  },
  logoutContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    padding: 16,
  },
  logoutIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  logoutText: {
    color: Colors.light.error,
    fontWeight: '600',
    fontSize: 16,
    flex: 1,
  },
  versionText: {
    textAlign: 'center',
    color: Colors.light.neutral[400],
    fontSize: 12,
    marginBottom: 20,
  },
});