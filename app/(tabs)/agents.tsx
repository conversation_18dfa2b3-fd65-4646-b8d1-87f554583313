import { LinearGradient } from "expo-linear-gradient";
import React, { useRef, useState } from 'react';
import {
    Animated,
    Dimensions,
    FlatList,
    Modal,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';

const { width, height } = Dimensions.get('window');

// Sample data for agents
const SAMPLE_AGENTS = [
  { id: '1', name: '<PERSON>', phone: '+256 701 234 567', type: 'agent', station: 'Kampala Central A', status: 'active' },
  { id: '2', name: '<PERSON>', phone: '+256 702 345 678', type: 'agent', station: 'Kampala Central B', status: 'active' },
  { id: '3', name: '<PERSON>', phone: '+256 703 456 789', type: 'manager', station: null, status: 'active' },
  { id: '4', name: '<PERSON>', phone: '+256 704 567 890', type: 'agent', station: '<PERSON><PERSON><PERSON>', status: 'inactive' },
  { id: '5', name: '<PERSON>', phone: '+256 705 678 901', type: 'agent', station: 'Kololo', status: 'active' },
  { id: '6', name: 'Sarah <PERSON>', phone: '+256 706 789 012', type: 'agent', station: 'Bukoto', status: 'active' },
  { id: '7', name: 'Michael Davis', phone: '+256 707 890 123', type: 'agent', station: 'Ntinda', status: 'inactive' },
];

type AgentItemProps = {
  agent: {
    id: string;
    name: string;
    phone: string;
    type: 'agent' | 'manager';
    station: string | null;
    status: 'active' | 'inactive';
  };
  onPress: () => void;
};

const AgentItem = ({ agent, onPress }: AgentItemProps) => {
  return (
    <TouchableOpacity
      style={styles.agentItem}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={[
        styles.agentAvatar,
        agent.type === 'manager' ? styles.managerAvatar : styles.regularAgentAvatar,
        agent.status === 'inactive' && styles.inactiveAvatar
      ]}>
        <ThemedText style={styles.avatarText}>
          {agent.name.split(' ').map(n => n[0]).join('')}
        </ThemedText>
      </View>

      <View style={styles.agentInfo}>
        <View style={styles.agentNameRow}>
          <ThemedText style={styles.agentName}>{agent.name}</ThemedText>
          {agent.type === 'manager' && (
            <View style={styles.managerBadge}>
              <ThemedText style={styles.managerBadgeText}>Manager</ThemedText>
            </View>
          )}
        </View>

        <ThemedText style={styles.agentPhone}>{agent.phone}</ThemedText>

        {agent.station && (
          <View style={styles.stationContainer}>
            <IconSymbol size={14} name="mappin.and.ellipse" color={Colors.light.neutral[500]} />
            <ThemedText style={styles.stationText}>{agent.station}</ThemedText>
          </View>
        )}
      </View>

      <View style={[
        styles.statusIndicator,
        agent.status === 'active' ? styles.activeIndicator : styles.inactiveIndicator
      ]} />
    </TouchableOpacity>
  );
};

export default function AgentsScreen() {
  const [agents, setAgents] = useState(SAMPLE_AGENTS);
  const [filteredAgents, setFilteredAgents] = useState(SAMPLE_AGENTS);
  const [modalVisible, setModalVisible] = useState(false);
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [agentType, setAgentType] = useState<'agent' | 'manager'>('agent');
  const [station, setStation] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Animation for FAB
  const fabAnim = useRef(new Animated.Value(0)).current;

  const handleAddAgent = () => {
    // Validate inputs
    if (!name.trim() || !phone.trim()) {
      // Show error
      return;
    }

    // Create new agent
    const newAgent = {
      id: (agents.length + 1).toString(),
      name: name.trim(),
      phone: phone.trim(),
      type: agentType,
      station: agentType === 'agent' ? station.trim() : null,
      status: 'active' as const
    };

    // Add to list
    setAgents([...agents, newAgent]);

    // Reset form and close modal
    setName('');
    setPhone('');
    setAgentType('agent');
    setStation('');
    setModalVisible(false);
  };

  const handleAgentPress = (agent: any) => {
    console.log('Agent pressed:', agent);
    // Navigate to agent details or show options
  };

  // Filter agents based on active filter and search query
  const filterAgents = React.useCallback(() => {
    let result = [...agents];

    // Apply type filter
    if (activeFilter === 'agents') {
      result = result.filter(agent => agent.type === 'agent');
    } else if (activeFilter === 'managers') {
      result = result.filter(agent => agent.type === 'manager');
    }

    // Apply search filter if there's a query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      result = result.filter(
        agent =>
          agent.name.toLowerCase().includes(query) ||
          agent.phone.toLowerCase().includes(query) ||
          (agent.station && agent.station.toLowerCase().includes(query))
      );
    }

    setFilteredAgents(result);
  }, [agents, activeFilter, searchQuery]);

  // Apply filters when dependencies change
  React.useEffect(() => {
    filterAgents();
  }, [filterAgents, agents, activeFilter, searchQuery]);

  // Animate FAB on mount
  React.useEffect(() => {
    Animated.spring(fabAnim, {
      toValue: 1,
      useNativeDriver: true,
      friction: 6,
    }).start();
  }, []);

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={[Colors.light.primary, Colors.light.secondary]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <ThemedText style={styles.headerTitle}>Agents & Managers</ThemedText>
        </View>
      </LinearGradient>

      {/* Search field - moved below the title */}
      <View style={styles.searchWrapper}>
        <View style={styles.searchContainer}>
          <IconSymbol size={20} name="magnifyingglass" color={Colors.light.neutral[500]} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search agents..."
            placeholderTextColor={Colors.light.neutral[500]}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <IconSymbol size={18} name="xmark.circle.fill" color={Colors.light.neutral[400]} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Tabs for filtering */}
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterTab, activeFilter === 'all' && styles.activeFilterTab]}
          onPress={() => setActiveFilter('all')}
        >
          <ThemedText style={[styles.filterText, activeFilter === 'all' && styles.activeFilterText]}>
            All
          </ThemedText>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterTab, activeFilter === 'agents' && styles.activeFilterTab]}
          onPress={() => setActiveFilter('agents')}
        >
          <ThemedText style={[styles.filterText, activeFilter === 'agents' && styles.activeFilterText]}>
            Agents
          </ThemedText>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterTab, activeFilter === 'managers' && styles.activeFilterTab]}
          onPress={() => setActiveFilter('managers')}
        >
          <ThemedText style={[styles.filterText, activeFilter === 'managers' && styles.activeFilterText]}>
            Managers
          </ThemedText>
        </TouchableOpacity>
      </View>

      {/* Agents List */}
      <FlatList
        data={filteredAgents}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <AgentItem agent={item} onPress={() => handleAgentPress(item)} />
        )}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <IconSymbol size={50} name="person.2.slash.fill" color={Colors.light.neutral[300]} />
            <ThemedText style={styles.emptyText}>No agents found</ThemedText>
            <ThemedText style={styles.emptySubtext}>
              {searchQuery ? "Try a different search term" : "Add agents using the + button"}
            </ThemedText>
          </View>
        }
      />

      {/* FAB */}
      <Animated.View
        style={[
          styles.fabContainer,
          {
            transform: [
              { scale: fabAnim },
              { translateY: fabAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [100, 0]
              })}
            ]
          }
        ]}
      >
        <TouchableOpacity
          style={styles.fab}
          onPress={() => setModalVisible(true)}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={[Colors.light.primary, Colors.light.secondary]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.fabGradient}
          >
            <IconSymbol size={24} name="plus" color="white" />
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>

      {/* Add Agent Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>Add New Team Member</ThemedText>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <IconSymbol size={24} name="xmark" color={Colors.light.neutral[500]} />
              </TouchableOpacity>
            </View>

            <View style={styles.formContainer}>
              <View style={styles.inputContainer}>
                <ThemedText style={styles.inputLabel}>Full Name</ThemedText>
                <TextInput
                  style={styles.input}
                  value={name}
                  onChangeText={setName}
                  placeholder="Enter full name"
                  placeholderTextColor={Colors.light.placeholder}
                />
              </View>

              <View style={styles.inputContainer}>
                <ThemedText style={styles.inputLabel}>Phone Number</ThemedText>
                <TextInput
                  style={styles.input}
                  value={phone}
                  onChangeText={setPhone}
                  placeholder="Enter phone number"
                  placeholderTextColor={Colors.light.placeholder}
                  keyboardType="phone-pad"
                />
              </View>

              <View style={styles.inputContainer}>
                <ThemedText style={styles.inputLabel}>Role</ThemedText>
                <View style={styles.roleContainer}>
                  <TouchableOpacity
                    style={[
                      styles.roleOption,
                      agentType === 'agent' && styles.selectedRole
                    ]}
                    onPress={() => setAgentType('agent')}
                  >
                    <IconSymbol
                      size={20}
                      name="person.fill"
                      color={agentType === 'agent' ? Colors.light.primary : Colors.light.neutral[500]}
                    />
                    <ThemedText style={[
                      styles.roleText,
                      agentType === 'agent' && styles.selectedRoleText
                    ]}>
                      Agent
                    </ThemedText>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.roleOption,
                      agentType === 'manager' && styles.selectedRole
                    ]}
                    onPress={() => setAgentType('manager')}
                  >
                    <IconSymbol
                      size={20}
                      name="person.fill.badge.plus"
                      color={agentType === 'manager' ? Colors.light.primary : Colors.light.neutral[500]}
                    />
                    <ThemedText style={[
                      styles.roleText,
                      agentType === 'manager' && styles.selectedRoleText
                    ]}>
                      Manager
                    </ThemedText>
                  </TouchableOpacity>
                </View>
              </View>

              {agentType === 'agent' && (
                <View style={styles.inputContainer}>
                  <ThemedText style={styles.inputLabel}>Polling Station</ThemedText>
                  <TextInput
                    style={styles.input}
                    value={station}
                    onChangeText={setStation}
                    placeholder="Assign polling station"
                    placeholderTextColor={Colors.light.placeholder}
                  />
                </View>
              )}

              <TouchableOpacity
                style={[
                  styles.addButton,
                  (!name.trim() || !phone.trim()) && styles.disabledButton
                ]}
                onPress={handleAddAgent}
                disabled={!name.trim() || !phone.trim()}
              >
                <LinearGradient
                  colors={[Colors.light.primary, Colors.light.secondary]}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={styles.buttonGradient}
                >
                  <ThemedText style={styles.buttonText}>Add Team Member</ThemedText>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.neutral[50],
  },
  header: {
    height: 110,
    paddingTop: 50,
    paddingHorizontal: 16,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
  },
  searchWrapper: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.neutral[100],
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.neutral[100],
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 10,
    width: '100%',
  },
  searchInput: {
    flex: 1,
    color: Colors.light.neutral[800],
    fontSize: 16,
    marginLeft: 8,
    height: 24,
  },
  filterContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  filterTab: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 8,
  },
  activeFilterTab: {
    backgroundColor: `${Colors.light.primary}15`,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.neutral[600],
  },
  activeFilterText: {
    color: Colors.light.primary,
    fontWeight: '600',
  },
  listContent: {
    padding: 16,
    paddingBottom: 100, // Extra space for FAB
  },
  agentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  agentAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  regularAgentAvatar: {
    backgroundColor: `${Colors.light.primary}15`,
  },
  managerAvatar: {
    backgroundColor: `${Colors.light.accent}15`,
  },
  inactiveAvatar: {
    backgroundColor: Colors.light.neutral[200],
  },
  avatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.primary,
  },
  agentInfo: {
    flex: 1,
  },
  agentNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  agentName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.neutral[800],
    marginRight: 8,
  },
  managerBadge: {
    backgroundColor: `${Colors.light.accent}15`,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  managerBadgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.light.accent,
  },
  agentPhone: {
    fontSize: 14,
    color: Colors.light.neutral[600],
    marginBottom: 4,
  },
  stationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stationText: {
    fontSize: 13,
    color: Colors.light.neutral[500],
    marginLeft: 4,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  activeIndicator: {
    backgroundColor: Colors.light.success,
  },
  inactiveIndicator: {
    backgroundColor: Colors.light.neutral[300],
  },
  fabContainer: {
    position: 'absolute',
    right: 20,
    bottom: 20,
  },
  fab: {
    width: 60,
    height: 60,
    borderRadius: 30,
    overflow: 'hidden',
    shadowColor: Colors.light.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  fabGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 20,
    paddingBottom: 30,
    maxHeight: height * 0.8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.neutral[100],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.neutral[800],
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.light.neutral[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  formContainer: {
    paddingTop: 20,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.neutral[700],
    marginBottom: 8,
  },
  input: {
    height: 50,
    borderWidth: 1.5,
    borderColor: Colors.light.neutral[200],
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: Colors.light.neutral[50],
    color: Colors.light.neutral[800],
  },
  roleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  roleOption: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '48%',
    height: 50,
    borderWidth: 1.5,
    borderColor: Colors.light.neutral[200],
    borderRadius: 12,
    paddingHorizontal: 16,
    backgroundColor: Colors.light.neutral[50],
  },
  selectedRole: {
    borderColor: Colors.light.primary,
    backgroundColor: `${Colors.light.primary}10`,
  },
  roleText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.neutral[600],
    marginLeft: 8,
  },
  selectedRoleText: {
    color: Colors.light.primary,
  },
  addButton: {
    height: 56,
    borderRadius: 12,
    overflow: 'hidden',
    marginTop: 10,
  },
  buttonGradient: {
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.6,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.neutral[600],
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: Colors.light.neutral[500],
    marginTop: 8,
    textAlign: 'center',
  },
});