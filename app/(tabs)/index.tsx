import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import React, { useEffect, useState } from 'react';
import {
    Dimensions,
    Image,
    Modal,
    Pressable,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';

import ParallaxScrollView from '@/components/ParallaxScrollView';
import ScreenWrapper from '@/components/ScreenWrapper';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/context/AuthContext';

const { width } = Dimensions.get('window');

// Types
type ActivityItem = {
  icon: string;
  text: string;
  timestamp: string;
  type: 'voter' | 'agent' | 'system';
};

type StatCardProps = {
  icon: string;
  label: string;
  value: string;
  color: string;
  onPress?: () => void;
};

type QuickActionProps = {
  icon: string;
  label: string;
  color: string;
  onPress: () => void;
};

// Sample data
const activityItems: ActivityItem[] = [
  {
    icon: "person.fill",
    text: "Agent Rahul came online at Station 12",
    timestamp: "2 hours ago",
    type: 'agent'
  },
  {
    icon: "doc.text.fill",
    text: "Agent Priya uploaded DR form from Station 8",
    timestamp: "4 hours ago",
    type: 'voter'
  },
  {
    icon: "person.fill.badge.plus",
    text: "You added Agent Amit for Station 15",
    timestamp: "Yesterday",
    type: 'agent'
  },
  {
    icon: "exclamationmark.triangle.fill",
    text: "4 agents currently offline",
    timestamp: "30 minutes ago",
    type: 'system'
  },
];

// Components
const StatCard = ({ icon, label, value, color, onPress }: StatCardProps) => {
  return (
    <Pressable
      style={({ pressed }) => [
        styles.statCard,
        pressed && styles.cardPressed
      ]}
      onPress={onPress}
    >
      <View style={styles.statCardContent}>
        <View style={[styles.iconContainer, { backgroundColor: `${color}15` }]}>
          <IconSymbol size={22} name={icon} color={color} />
        </View>
        <View style={styles.statTextContainer}>
          <ThemedText style={styles.statLabel}>{label}</ThemedText>
          <View style={styles.valueRow}>
            <ThemedText style={styles.statValue}>{value}</ThemedText>
          </View>
        </View>
      </View>
    </Pressable>
  );
};

const QuickAction = ({ icon, label, color, onPress }: QuickActionProps) => {
  return (
    <TouchableOpacity
      style={styles.quickAction}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={[styles.quickActionIcon, { backgroundColor: `${color}15` }]}>
        <IconSymbol size={20} name={icon} color={color} />
      </View>
      <ThemedText style={styles.quickActionLabel}>{label}</ThemedText>
    </TouchableOpacity>
  );
};

export default function DashboardScreen() {
  const { user } = useAuth();
  const [greeting, setGreeting] = useState(() => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good Morning";
    if (hour < 18) return "Good Afternoon";
    return "Good Evening";
  });

  // DR Form state management - now synced with user data
  const [drFormStatus, setDrFormStatus] = useState(() => {
    // Initialize based on user's actual DR form status if available
    return user?.drFormStatus || 0;
  });
  const [drFormImage, setDrFormImage] = useState(() => {
    // Initialize with user's DR form image if available
    return user?.drFormImage || "";
  });
  const [imageModalVisible, setImageModalVisible] = useState(false);

  // Dashboard data state - centralized data management
  const [dashboardData, setDashboardData] = useState(() => {
    // Initialize with calculated data based on current system state
    const totalStations = 24; // This should come from actual polling stations data
    const totalAgents = totalStations; // Each station has exactly one agent
    const collectedForms = user?.role === 'agent' ? (drFormStatus >= 2 ? 1 : 0) : 12; // Agent: their form, Others: system total
    const activeForms = user?.role === 'agent' ? 1 : totalStations; // Agent: always 1, Others: total stations
    const activeAgents = 20; // Currently online agents
    const offlineAgents = totalAgents - activeAgents;
    const missingForms = totalStations - collectedForms;

    return {
      totalStations,
      totalAgents,
      collectedForms,
      activeForms,
      activeAgents,
      offlineAgents,
      missingForms,
      completionPercentage: user?.role === 'agent'
        ? (drFormStatus >= 3 ? 100 : drFormStatus >= 2 ? 66 : drFormStatus >= 1 ? 33 : 0)
        : Math.round((collectedForms / totalStations) * 100)
    };
  });

  // Determine user role
  const isHon = user?.role === 'hon';
  const isManager = user?.role === 'manager';
  const isAgent = user?.role === 'agent';

  // Helper functions for DR Form status
  const getDrFormProgress = () => {
    switch (drFormStatus) {
      case 0: return 0;
      case 1: return 33;
      case 2: return 66;
      case 3: return 100;
      default: return 0;
    }
  };

  const getDrFormStatusText = () => {
    switch (drFormStatus) {
      case 0: return "Pending";
      case 1: return "Captured";
      case 2: return "Uploaded";
      case 3: return "Verified";
      default: return "Pending";
    }
  };

  const getDrFormStatusIcon = () => {
    switch (drFormStatus) {
      case 0: return "clock.fill";
      case 1: return "camera.fill";
      case 2: return "arrow.up.circle.fill";
      case 3: return "checkmark.circle.fill";
      default: return "clock.fill";
    }
  };

  const getDrFormStatusBadgeStyle = () => {
    switch (drFormStatus) {
      case 0: return styles.pendingBadge;
      case 1: return styles.capturedBadge;
      case 2: return styles.uploadedBadge;
      case 3: return styles.completedBadge;
      default: return styles.pendingBadge;
    }
  };

  const handleQuickAction = (action: string) => {
    console.log(`Quick action: ${action}`);
    // Implement action handling
  };

  // Synchronized DR form handling with dashboard data updates
  const handleUploadDrForm = () => {
    if (drFormStatus === 0) {
      // Simulate capturing
      const newStatus = 1;
      setDrFormStatus(newStatus);
      setDrFormImage("https://via.placeholder.com/400x300/4F46E5/FFFFFF?text=DR+Form+Captured");

      // Update dashboard data to reflect the change
      setDashboardData(prev => ({
        ...prev,
        completionPercentage: isAgent ? 33 : prev.completionPercentage
      }));

      // Simulate upload after 2 seconds
      setTimeout(() => {
        const uploadStatus = 2;
        setDrFormStatus(uploadStatus);

        // Update dashboard data for upload
        setDashboardData(prev => ({
          ...prev,
          collectedForms: isAgent ? 1 : prev.collectedForms + 1,
          missingForms: isAgent ? 0 : prev.missingForms - 1,
          completionPercentage: isAgent ? 66 : Math.round(((prev.collectedForms + 1) / prev.totalStations) * 100)
        }));

        // Simulate verification after another 2 seconds
        setTimeout(() => {
          const verifiedStatus = 3;
          setDrFormStatus(verifiedStatus);

          // Update dashboard data for verification
          setDashboardData(prev => ({
            ...prev,
            completionPercentage: isAgent ? 100 : prev.completionPercentage
          }));
        }, 2000);
      }, 2000);
    } else {
      // Navigate to upload screen for retaking
      router.navigate("/(tabs)/upload-dr");
    }
  };

  return (
    <ScreenWrapper backgroundColor={Colors.light.neutral[50]}>
      <ParallaxScrollView
        headerBackgroundColor={{ light: Colors.light.primary, dark: Colors.dark.primary }}
        headerHeight={220}
        headerImage={
        <LinearGradient
          colors={[Colors.light.primary, Colors.light.secondary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerTextContent}>
              <ThemedText style={styles.greeting}>{greeting}</ThemedText>
              <ThemedText style={styles.headerName}>{user?.name || 'User'}</ThemedText>
              <ThemedText style={styles.headerSubtitle}>
                {isHon ? 'Honorable Member' : isManager ? 'Campaign Manager' : 'Polling Agent'}
              </ThemedText>
            </View>
            <TouchableOpacity
              style={styles.profileButton}
              activeOpacity={0.7}
              onPress={() => {
                // Using navigate instead of push for smoother transitions within tabs
                router.navigate("/(tabs)/profile");
              }}
            >
              <View style={styles.profileAvatar}>
                <ThemedText style={styles.profileAvatarText}>
                  {user?.name.split(' ').map(n => n[0]).join('')}
                </ThemedText>
              </View>
            </TouchableOpacity>
          </View>
          <View style={styles.headerDecoration}>
            <IconSymbol
              size={200}
              color="rgba(255,255,255,0.1)"
              name="chart.bar.fill"
            />
          </View>
        </LinearGradient>
      }
    >
      <ThemedView style={styles.container}>
        {/* Quick Actions - Only visible to managers/Hon, not agents */}
        {!isAgent && (
          <>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionTitleContainer}>
                <IconSymbol name="bolt.fill" size={20} color={Colors.light.neutral[800]} style={styles.sectionIcon} />
                <ThemedText style={styles.sectionTitle}>Quick Actions</ThemedText>
              </View>
            </View>

            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.quickActionsContainer}
            >
              {/* View Stats - visible to managers/Hon */}
              <QuickAction
                icon="chart.pie.fill"
                label="View Stats"
                color="#3B82F6"
                onPress={() => router.navigate("/(tabs)/statistics")}
              />

              {/* Add Agent - visible only to Hon/admin and managers */}
              {(isHon || isManager) && (
                <QuickAction
                  icon="person.fill.badge.plus"
                  label="Add Agent"
                  color="#F59E0B"
                  onPress={() => router.navigate("/(tabs)/add-agent")}
                />
              )}

              {/* Reports - visible to managers/Hon */}
              <QuickAction
                icon="doc.richtext.fill"
                label="Reports"
                color="#8B5CF6"
                onPress={() => router.navigate("/(tabs)/statistics")}
              />
            </ScrollView>
          </>
        )}



        {/* Stats Grid - Different view for agents vs managers/hon */}
        <View style={styles.sectionHeader}>
          <View style={styles.sectionTitleContainer}>
            <IconSymbol name="chart.bar.fill" size={20} color={Colors.light.neutral[800]} style={styles.sectionIcon} />
            <ThemedText style={styles.sectionTitle}>
              {isAgent ? "Your Polling Station" : "Campaign Overview"}
            </ThemedText>
          </View>
        </View>

        {isAgent ? (
          // Agent-specific enhanced station overview
          <>
            {/* Main Station Card */}
            <ThemedView style={styles.agentStationCard}>
              <View style={styles.stationCardHeader}>
                <View style={styles.stationHeaderLeft}>
                  <View style={styles.stationIconContainer}>
                    <IconSymbol name="building.2.fill" size={24} color="#FFF" />
                  </View>
                  <View style={styles.stationHeaderText}>
                    <ThemedText style={styles.stationName}>
                      {user?.station || "Station Assignment Pending"}
                    </ThemedText>
                    <ThemedText style={styles.stationSubtitle}>Your Assigned Polling Station</ThemedText>
                  </View>
                </View>
                <View style={[styles.statusBadge, styles.activeStatusBadge]}>
                  <View style={styles.statusDot} />
                  <ThemedText style={styles.statusText}>Online</ThemedText>
                </View>
              </View>
            </ThemedView>

            {/* Quick Stats Grid */}
            <View style={styles.agentStatsContainer}>
              <View style={styles.statsRow}>
                <View style={styles.agentStatCard}>
                  <View style={styles.agentStatHeader}>
                    <IconSymbol name="doc.text.fill" size={20} color="#3B82F6" />
                    <ThemedText style={styles.agentStatValue}>
                      {dashboardData.collectedForms}/{dashboardData.activeForms}
                    </ThemedText>
                  </View>
                  <ThemedText style={styles.agentStatLabel}>DR Forms</ThemedText>
                  <View style={styles.agentStatProgress}>
                    <View style={[
                      styles.agentStatProgressBar,
                      {
                        width: `${(dashboardData.collectedForms / dashboardData.activeForms) * 100}%`,
                        backgroundColor: '#3B82F6'
                      }
                    ]} />
                  </View>
                </View>

                <View style={styles.agentStatCard}>
                  <View style={styles.agentStatHeader}>
                    <IconSymbol name="checkmark.circle.fill" size={20} color="#10B981" />
                    <ThemedText style={styles.agentStatValue}>{dashboardData.completionPercentage}%</ThemedText>
                  </View>
                  <ThemedText style={styles.agentStatLabel}>Completion</ThemedText>
                  <View style={styles.agentStatProgress}>
                    <View style={[
                      styles.agentStatProgressBar,
                      {
                        width: `${dashboardData.completionPercentage}%`,
                        backgroundColor: '#10B981'
                      }
                    ]} />
                  </View>
                </View>
              </View>
            </View>
          </>
        ) : (
          // Manager/Hon view
          <>
            <View style={styles.statsGrid}>
              <StatCard
                icon="doc.text.fill"
                label="Collected DR Forms"
                value={`${dashboardData.collectedForms}/${dashboardData.totalStations}`}
                color="#3B82F6"
                onPress={() => router.navigate("/(screens)/collected-forms")}
              />
              <StatCard
                icon="building.2.fill"
                label="Polling Stations"
                value={dashboardData.totalStations.toString()}
                color="#10B981"
              />
            </View>

            <View style={styles.statsGrid}>
              <StatCard
                icon="person.2.fill"
                label="Total Agents"
                value={dashboardData.totalAgents.toString()}
                color="#8B5CF6"
              />
              <StatCard
                icon="exclamationmark.triangle.fill"
                label="Missing Forms"
                value={dashboardData.missingForms.toString()}
                color="#EF4444"
              />
            </View>

            <View style={styles.statsGrid}>
              <StatCard
                icon="person.fill"
                label="Active Agents"
                value={dashboardData.activeAgents.toString()}
                color="#F59E0B"
              />
              <StatCard
                icon="person.2.circle"
                label="Offline Agents"
                value={dashboardData.offlineAgents.toString()}
                color="#6B7280"
              />
            </View>
          </>
        )}

        {/* DR Form Collection Progress - Different for agents vs managers/hon */}
        <View style={styles.sectionHeader}>
          <View style={styles.sectionTitleContainer}>
            <IconSymbol name="clipboard.fill" size={20} color={Colors.light.neutral[800]} style={styles.sectionIcon} />
            <ThemedText style={styles.sectionTitle}>
              {isAgent ? "Your DR Form Status" : "DR Form Collection"}
            </ThemedText>
          </View>
        </View>

        {isAgent ? (
          // Agent-specific enhanced DR form section
          <ThemedView style={styles.agentDrFormCard}>
            <View style={styles.drFormCardHeader}>
              <View style={styles.drFormHeaderLeft}>
                <IconSymbol name="doc.text.fill" size={24} color={Colors.light.primary} />
                <View style={styles.drFormHeaderText}>
                  <ThemedText style={styles.drFormCardTitle}>DR Form Submission</ThemedText>
                  <ThemedText style={styles.drFormCardSubtitle}>Document your polling station results</ThemedText>
                </View>
              </View>
              <View style={[styles.drFormStatusBadge, getDrFormStatusBadgeStyle()]}>
                <IconSymbol name={getDrFormStatusIcon()} size={16} color="#FFF" />
                <ThemedText style={styles.drFormStatusText}>{getDrFormStatusText()}</ThemedText>
              </View>
            </View>

            {user?.station ? (
              <>
                <View style={styles.drFormProgress}>
                  <View style={styles.drFormProgressHeader}>
                    <ThemedText style={styles.drFormProgressTitle}>Submission Progress</ThemedText>
                    <ThemedText style={styles.drFormProgressPercentage}>{getDrFormProgress()}%</ThemedText>
                  </View>
                  <View style={styles.drFormProgressBar}>
                    <View style={[styles.drFormProgressFill, { width: `${getDrFormProgress()}%` }]} />
                  </View>
                  <View style={styles.drFormProgressSteps}>
                    <View style={styles.drFormStep}>
                      <View style={[styles.drFormStepIcon, drFormStatus >= 1 ? styles.completedStep : styles.pendingStep]}>
                        {drFormStatus >= 1 ? (
                          <IconSymbol name="checkmark" size={12} color="#FFF" />
                        ) : (
                          <ThemedText style={styles.stepNumber}>1</ThemedText>
                        )}
                      </View>
                      <ThemedText style={styles.drFormStepText}>Captured</ThemedText>
                    </View>
                    <View style={styles.drFormStep}>
                      <View style={[styles.drFormStepIcon, drFormStatus >= 2 ? styles.completedStep : styles.pendingStep]}>
                        {drFormStatus >= 2 ? (
                          <IconSymbol name="checkmark" size={12} color="#FFF" />
                        ) : (
                          <ThemedText style={styles.stepNumber}>2</ThemedText>
                        )}
                      </View>
                      <ThemedText style={styles.drFormStepText}>Uploaded</ThemedText>
                    </View>
                    <View style={styles.drFormStep}>
                      <View style={[styles.drFormStepIcon, drFormStatus >= 3 ? styles.completedStep : styles.pendingStep]}>
                        {drFormStatus >= 3 ? (
                          <IconSymbol name="checkmark" size={12} color="#FFF" />
                        ) : (
                          <ThemedText style={styles.stepNumber}>3</ThemedText>
                        )}
                      </View>
                      <ThemedText style={styles.drFormStepText}>Verified</ThemedText>
                    </View>
                  </View>
                </View>

                {/* DR Form Image Preview (if captured) */}
                {drFormStatus >= 1 && drFormImage && (
                  <TouchableOpacity
                    style={styles.drFormImageContainer}
                    onPress={() => setImageModalVisible(true)}
                  >
                    <Image source={{ uri: drFormImage }} style={styles.drFormImage} />
                    <View style={styles.imageOverlay}>
                      <IconSymbol name="eye.fill" size={20} color="#FFF" />
                      <ThemedText style={styles.imageOverlayText}>Tap to view full screen</ThemedText>
                    </View>
                  </TouchableOpacity>
                )}

                {/* Action Button */}
                <TouchableOpacity
                  style={[styles.uploadDrButton, drFormStatus >= 3 && styles.completedButton]}
                  onPress={handleUploadDrForm}
                >
                  <LinearGradient
                    colors={drFormStatus >= 3 ? ['#10B981', '#059669'] : [Colors.light.primary, Colors.light.secondary]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.uploadDrGradient}
                  >
                    <IconSymbol
                      size={20}
                      name={drFormStatus >= 1 ? "camera.fill" : "camera.fill"}
                      color="white"
                      style={styles.uploadDrIcon}
                    />
                    <ThemedText style={styles.uploadDrText}>
                      {drFormStatus >= 1 ? "Retake DR Form" : "Upload DR Form"}
                    </ThemedText>
                  </LinearGradient>
                </TouchableOpacity>
              </>
            ) : (
              <View style={styles.noStationContainer}>
                <IconSymbol name="exclamationmark.triangle.fill" size={40} color={Colors.light.warning} />
                <ThemedText style={styles.noStationText}>
                  You haven't been assigned to a polling station yet.
                </ThemedText>
                <ThemedText style={styles.noStationSubtext}>
                  Please contact your campaign manager.
                </ThemedText>
              </View>
            )}
          </ThemedView>
        ) : (
          // Manager/Hon DR form card
          <ThemedView style={styles.drFormCard}>
            <View style={styles.drFormHeader}>
              <ThemedText style={styles.drFormTitle}>Collection Progress</ThemedText>
              <View style={styles.drFormBadge}>
                <ThemedText style={styles.drFormBadgeText}>{dashboardData.completionPercentage}% Complete</ThemedText>
              </View>
            </View>

            <View style={styles.drFormBarContainer}>
              <View style={styles.drFormBarBackground}>
                <View style={[styles.drFormBarFill, { width: `${dashboardData.completionPercentage}%` }]} />
              </View>
            </View>

            <View style={styles.drFormStations}>
              <View style={styles.drFormStationItem}>
                <IconSymbol name="checkmark.circle.fill" size={18} color={Colors.light.success} style={styles.drFormStationIcon} />
                <ThemedText style={styles.drFormStationText}>{dashboardData.collectedForms} Collected</ThemedText>
              </View>
              <View style={styles.drFormStationItem}>
                <IconSymbol name="xmark.circle.fill" size={18} color={Colors.light.error} style={styles.drFormStationIcon} />
                <ThemedText style={styles.drFormStationText}>{dashboardData.missingForms} Missing</ThemedText>
              </View>
            </View>

            <TouchableOpacity
              style={styles.viewMissingButton}
              onPress={() => router.navigate("/(screens)/collected-forms")}
            >
              <ThemedText style={styles.viewMissingText}>View all DR Forms</ThemedText>
              <IconSymbol size={16} name="chevron.right" color={Colors.light.primary} />
            </TouchableOpacity>
          </ThemedView>
        )}

        {/* Recent Activity - Redesigned */}
        <ThemedView style={styles.enhancedActivityCard}>
          <View style={styles.enhancedActivityHeader}>
            <View style={styles.activityHeaderLeft}>
              <IconSymbol name="clock.fill" size={20} color={Colors.light.primary} />
              <ThemedText style={styles.enhancedActivityTitle}>
                {isAgent ? "Your Activity" : "Recent Activity"}
              </ThemedText>
            </View>
            <TouchableOpacity style={styles.viewAllActivityButton}>
              <ThemedText style={styles.viewAllActivityText}>View All</ThemedText>
              <IconSymbol size={14} name="chevron.right" color={Colors.light.primary} />
            </TouchableOpacity>
          </View>

          {isAgent ? (
            // Agent-specific enhanced activity items
            <>
              <View style={styles.enhancedActivityItem}>
                <View style={styles.activityLeftSection}>
                  <View style={[styles.enhancedActivityIcon, { backgroundColor: Colors.light.success }]}>
                    <IconSymbol size={20} name="person.fill" color="#FFF" />
                  </View>
                  <View style={styles.activityTimeline} />
                </View>
                <View style={styles.enhancedActivityContent}>
                  <View style={styles.activityItemHeader}>
                    <ThemedText style={styles.enhancedActivityItemTitle}>Login Session Started</ThemedText>
                    <View style={styles.activityStatusBadge}>
                      <View style={[styles.activityStatusDot, { backgroundColor: Colors.light.success }]} />
                      <ThemedText style={styles.activityStatusText}>Active</ThemedText>
                    </View>
                  </View>
                  <ThemedText style={styles.enhancedActivityDescription}>
                    Successfully logged into your agent dashboard
                  </ThemedText>
                  <ThemedText style={styles.enhancedTimestampText}>Just now</ThemedText>
                </View>
              </View>

              <View style={styles.enhancedActivityItem}>
                <View style={styles.activityLeftSection}>
                  <View style={[styles.enhancedActivityIcon, { backgroundColor: '#3B82F6' }]}>
                    <IconSymbol size={20} name="doc.text.fill" color="#FFF" />
                  </View>
                  <View style={styles.activityTimeline} />
                </View>
                <View style={styles.enhancedActivityContent}>
                  <View style={styles.activityItemHeader}>
                    <ThemedText style={styles.enhancedActivityItemTitle}>DR Form Submitted</ThemedText>
                    <View style={[styles.activityStatusBadge, { backgroundColor: `${Colors.light.success}15` }]}>
                      <View style={[styles.activityStatusDot, { backgroundColor: Colors.light.success }]} />
                      <ThemedText style={[styles.activityStatusText, { color: Colors.light.success }]}>Complete</ThemedText>
                    </View>
                  </View>
                  <ThemedText style={styles.enhancedActivityDescription}>
                    DR form for {user?.station} has been verified and processed
                  </ThemedText>
                  <ThemedText style={styles.enhancedTimestampText}>2 hours ago</ThemedText>
                </View>
              </View>

              <View style={[styles.enhancedActivityItem, styles.lastEnhancedActivityItem]}>
                <View style={styles.activityLeftSection}>
                  <View style={[styles.enhancedActivityIcon, { backgroundColor: Colors.light.warning }]}>
                    <IconSymbol size={20} name="mappin.and.ellipse" color="#FFF" />
                  </View>
                  <View style={styles.activityTimelineEnd} />
                </View>
                <View style={styles.enhancedActivityContent}>
                  <View style={styles.activityItemHeader}>
                    <ThemedText style={styles.enhancedActivityItemTitle}>Station Assignment</ThemedText>
                    <View style={[styles.activityStatusBadge, { backgroundColor: `${Colors.light.warning}15` }]}>
                      <View style={[styles.activityStatusDot, { backgroundColor: Colors.light.warning }]} />
                      <ThemedText style={[styles.activityStatusText, { color: Colors.light.warning }]}>Assigned</ThemedText>
                    </View>
                  </View>
                  <ThemedText style={styles.enhancedActivityDescription}>
                    You were assigned to {user?.station || "polling station"}
                  </ThemedText>
                  <ThemedText style={styles.enhancedTimestampText}>1 day ago</ThemedText>
                </View>
              </View>
            </>
          ) : (
            // Manager/Hon enhanced activity items
            activityItems.map((item, index) => (
              <View key={index} style={[
                styles.enhancedActivityItem,
                index === activityItems.length - 1 && styles.lastEnhancedActivityItem
              ]}>
                <View style={styles.activityLeftSection}>
                  <View style={[styles.enhancedActivityIcon, styles[`${item.type}Icon`]]}>
                    <IconSymbol size={20} name={item.icon} color="#FFF" />
                  </View>
                  {index < activityItems.length - 1 ? (
                    <View style={styles.activityTimeline} />
                  ) : (
                    <View style={styles.activityTimelineEnd} />
                  )}
                </View>
                <View style={styles.enhancedActivityContent}>
                  <View style={styles.activityItemHeader}>
                    <ThemedText style={styles.enhancedActivityItemTitle}>{item.title || item.text}</ThemedText>
                    <View style={[styles.activityStatusBadge, { backgroundColor: `${Colors.light.primary}15` }]}>
                      <View style={[styles.activityStatusDot, { backgroundColor: Colors.light.primary }]} />
                      <ThemedText style={[styles.activityStatusText, { color: Colors.light.primary }]}>System</ThemedText>
                    </View>
                  </View>
                  <ThemedText style={styles.enhancedActivityDescription}>{item.text}</ThemedText>
                  <ThemedText style={styles.enhancedTimestampText}>{item.timestamp}</ThemedText>
                </View>
              </View>
            ))
          )}
        </ThemedView>
      </ThemedView>
    </ParallaxScrollView>

    {/* Image Modal for DR Form */}
    <Modal
      visible={imageModalVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setImageModalVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <TouchableOpacity
          style={styles.modalCloseArea}
          onPress={() => setImageModalVisible(false)}
        >
          <View style={styles.modalContent}>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setImageModalVisible(false)}
            >
              <IconSymbol name="xmark.circle.fill" size={32} color="#FFF" />
            </TouchableOpacity>

            <Image
              source={{ uri: drFormImage }}
              style={styles.modalImage}
              resizeMode="contain"
            />

            <View style={styles.modalInfo}>
              <ThemedText style={styles.modalTitle}>DR Form Image</ThemedText>
              <ThemedText style={styles.modalSubtitle}>
                Status: {getDrFormStatusText()} • {user?.station}
              </ThemedText>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    </Modal>

    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  // Header styles
  headerGradient: {
    flex: 1,
    width: '100%',
    height: '100%',
    position: 'relative',
    overflow: 'hidden',
  },
  headerContent: {
    position: 'absolute',
    top: 60,
    left: 20,
    right: 20,
    zIndex: 2,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerTextContent: {
    flex: 1,
  },
  profileButton: {
    marginLeft: 16,
  },
  profileAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  profileAvatarText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },
  headerDecoration: {
    position: 'absolute',
    bottom: -50,
    right: -50,
    opacity: 0.3,
    zIndex: 1,
  },
  greeting: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  headerName: {
    color: 'white',
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 16,
    fontWeight: '500',
  },

  // Section styles
  sectionHeader: {
    marginTop: 24,
    marginBottom: 16,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIcon: {
    marginRight: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.neutral[800],
  },

  drFormCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  drFormHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  drFormTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.neutral[800],
  },
  drFormBadge: {
    backgroundColor: `${Colors.light.success}15`,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  drFormBadgeText: {
    fontSize: 13,
    fontWeight: '600',
    color: Colors.light.success,
  },
  drFormBarContainer: {
    marginBottom: 16,
  },
  drFormBarBackground: {
    height: 12,
    backgroundColor: Colors.light.neutral[100],
    borderRadius: 6,
    overflow: 'hidden',
  },
  drFormBarFill: {
    height: '100%',
    backgroundColor: Colors.light.success,
    borderRadius: 6,
  },
  drFormStations: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  drFormStationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  drFormStationIcon: {
    marginRight: 8,
  },
  drFormStationText: {
    fontSize: 14,
    color: Colors.light.neutral[600],
  },
  viewMissingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.light.neutral[100],
  },
  viewMissingText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
    marginRight: 4,
  },
  // Agent-specific styles - Enhanced Station Card
  agentStationCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    marginBottom: 20,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 24,
    elevation: 8,
    borderWidth: 1,
    borderColor: Colors.light.neutral[50],
  },
  stationCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  stationHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  stationIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 16,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  stationHeaderText: {
    flex: 1,
  },
  stationName: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.neutral[900],
    marginBottom: 4,
    lineHeight: 24,
  },
  stationSubtitle: {
    fontSize: 14,
    color: Colors.light.neutral[500],
    fontWeight: '500',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginLeft: 12,
  },
  activeStatusBadge: {
    backgroundColor: `${Colors.light.success}15`,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.light.success,
    marginRight: 6,
  },
  statusText: {
    fontSize: 13,
    fontWeight: '600',
    color: Colors.light.success,
  },
  stationDetails: {
    backgroundColor: Colors.light.neutral[25],
    borderRadius: 16,
    padding: 16,
  },
  stationDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  stationDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  stationDetailText: {
    fontSize: 14,
    color: Colors.light.neutral[600],
    marginLeft: 8,
    fontWeight: '500',
  },

  // Agent Stats Container
  agentStatsContainer: {
    marginBottom: 20,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  agentStatCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    width: '48%',
    shadowColor: Colors.light.neutral[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 3,
    borderWidth: 1,
    borderColor: Colors.light.neutral[50],
  },
  agentStatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  agentStatValue: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.neutral[900],
  },
  agentStatLabel: {
    fontSize: 13,
    color: Colors.light.neutral[500],
    fontWeight: '500',
    marginBottom: 8,
  },
  agentStatProgress: {
    height: 4,
    backgroundColor: Colors.light.neutral[100],
    borderRadius: 2,
    overflow: 'hidden',
  },
  agentStatProgressBar: {
    height: '100%',
    borderRadius: 2,
  },

  // Enhanced DR Form Card
  agentDrFormCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    marginBottom: 20,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 24,
    elevation: 8,
    borderWidth: 1,
    borderColor: Colors.light.neutral[50],
  },
  drFormCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  drFormHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  drFormHeaderText: {
    marginLeft: 16,
    flex: 1,
  },
  drFormCardTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.neutral[900],
    marginBottom: 4,
  },
  drFormCardSubtitle: {
    fontSize: 14,
    color: Colors.light.neutral[500],
    fontWeight: '500',
  },
  drFormStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginLeft: 12,
  },
  completedBadge: {
    backgroundColor: Colors.light.success,
  },
  pendingBadge: {
    backgroundColor: Colors.light.neutral[400],
  },
  capturedBadge: {
    backgroundColor: '#3B82F6',
  },
  uploadedBadge: {
    backgroundColor: '#F59E0B',
  },
  drFormStatusText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#FFF',
    marginLeft: 4,
  },
  drFormProgress: {
    marginBottom: 24,
  },
  drFormProgressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  drFormProgressTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.neutral[800],
  },
  drFormProgressPercentage: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.success,
  },
  drFormProgressBar: {
    height: 8,
    backgroundColor: Colors.light.neutral[100],
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 16,
  },
  drFormProgressFill: {
    height: '100%',
    backgroundColor: Colors.light.success,
    borderRadius: 4,
  },
  drFormProgressSteps: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  drFormStep: {
    alignItems: 'center',
    flex: 1,
  },
  drFormStepIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  completedStep: {
    backgroundColor: Colors.light.success,
  },
  pendingStep: {
    backgroundColor: Colors.light.neutral[200],
  },
  stepNumber: {
    fontSize: 10,
    fontWeight: '600',
    color: Colors.light.neutral[600],
  },
  drFormStepText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.light.neutral[600],
    textAlign: 'center',
  },
  drFormActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  viewFormButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: `${Colors.light.primary}10`,
    borderWidth: 1,
    borderColor: `${Colors.light.primary}20`,
  },
  viewFormText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
    marginLeft: 8,
  },
  retakeFormButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: Colors.light.primary,
  },
  retakeFormText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFF',
    marginLeft: 8,
  },

  // Legacy agent styles (keeping for compatibility)
  agentStationInfo: {
    marginBottom: 20,
  },
  agentStationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  agentStationText: {
    fontSize: 16,
    color: Colors.light.neutral[700],
    marginLeft: 8,
    fontWeight: '500',
  },
  uploadDrButton: {
    height: 50,
    borderRadius: 12,
    overflow: 'hidden',
    marginTop: 10,
  },
  uploadDrGradient: {
    height: '100%',
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadDrIcon: {
    marginRight: 8,
  },
  uploadDrText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  noStationContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  noStationText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.neutral[700],
    marginTop: 16,
    textAlign: 'center',
  },
  noStationSubtext: {
    fontSize: 14,
    color: Colors.light.neutral[500],
    marginTop: 8,
    textAlign: 'center',
  },

  // Quick actions
  quickActionsContainer: {
    paddingRight: 16,
  },
  quickAction: {
    alignItems: 'center',
    marginRight: 16,
    width: 80,
  },
  quickActionIcon: {
    width: 56,
    height: 56,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  quickActionLabel: {
    fontSize: 13,
    fontWeight: '500',
    color: Colors.light.neutral[700],
    textAlign: 'center',
  },

  // Stat cards
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 16,
    width: '48%',
    minHeight: 80,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  cardPressed: {
    opacity: 0.9,
    transform: [{ scale: 0.98 }],
  },
  statCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  statTextContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  statLabel: {
    fontSize: 13,
    color: Colors.light.neutral[500],
    marginBottom: 6,
    fontWeight: '500',
    lineHeight: 16,
  },
  valueRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.neutral[800],
  },

  // Activity section
  activitySection: {
    marginTop: 24,
    marginBottom: 16,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  activityTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.neutral[800],
  },
  viewAllButton: {
    color: Colors.light.primary,
    fontSize: 14,
    fontWeight: '600',
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.neutral[100],
  },
  lastActivityItem: {
    borderBottomWidth: 0,
    paddingBottom: 0,
  },
  activityIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  voterIcon: {
    backgroundColor: '#3B82F6',
  },
  agentIcon: {
    backgroundColor: '#10B981',
  },
  systemIcon: {
    backgroundColor: '#F59E0B',
  },
  activityContent: {
    flex: 1,
  },
  activityText: {
    fontSize: 15,
    color: Colors.light.neutral[800],
    marginBottom: 4,
    fontWeight: '500',
  },
  timestampText: {
    fontSize: 12,
    color: Colors.light.neutral[500],
  },

  // DR Form Image Preview
  drFormImageContainer: {
    marginBottom: 20,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  drFormImage: {
    width: '100%',
    height: 200,
    backgroundColor: Colors.light.neutral[100],
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  imageOverlayText: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },

  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalCloseArea: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    position: 'relative',
  },
  modalCloseButton: {
    position: 'absolute',
    top: -50,
    right: 0,
    zIndex: 10,
  },
  modalImage: {
    width: '100%',
    height: 300,
    borderRadius: 12,
    backgroundColor: Colors.light.neutral[100],
  },
  modalInfo: {
    marginTop: 16,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#FFF',
    marginBottom: 4,
  },
  modalSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
  },

  // Completed button style
  completedButton: {
    opacity: 0.8,
  },

  // Enhanced Activity Section Styles
  enhancedActivityCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    marginBottom: 20,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 24,
    elevation: 8,
    borderWidth: 1,
    borderColor: Colors.light.neutral[50],
  },
  enhancedActivityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.neutral[100],
  },
  activityHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  enhancedActivityTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.neutral[900],
    marginLeft: 12,
  },
  viewAllActivityButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    backgroundColor: `${Colors.light.primary}10`,
  },
  viewAllActivityText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
    marginRight: 4,
  },
  enhancedActivityItem: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  lastEnhancedActivityItem: {
    marginBottom: 0,
  },
  activityLeftSection: {
    alignItems: 'center',
    marginRight: 16,
  },
  enhancedActivityIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  activityTimeline: {
    width: 2,
    flex: 1,
    backgroundColor: Colors.light.neutral[200],
    marginTop: 4,
  },
  activityTimelineEnd: {
    width: 2,
    height: 20,
    backgroundColor: Colors.light.neutral[200],
    marginTop: 4,
  },
  enhancedActivityContent: {
    flex: 1,
    paddingTop: 4,
  },
  activityItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  enhancedActivityItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.neutral[900],
    flex: 1,
    marginRight: 12,
  },
  activityStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: `${Colors.light.success}15`,
  },
  activityStatusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 4,
  },
  activityStatusText: {
    fontSize: 11,
    fontWeight: '600',
    color: Colors.light.success,
  },
  enhancedActivityDescription: {
    fontSize: 14,
    color: Colors.light.neutral[600],
    lineHeight: 20,
    marginBottom: 8,
  },
  enhancedTimestampText: {
    fontSize: 12,
    color: Colors.light.neutral[400],
    fontWeight: '500',
  },
});

