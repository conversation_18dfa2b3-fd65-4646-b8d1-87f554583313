import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import React, { useEffect, useRef, useState } from 'react';
import {
    Animated,
    Dimensions,
    Image,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/context/AuthContext';

const { width, height } = Dimensions.get('window');

// Sample image for preview
const sampleImage = 'https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dm90aW5nfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60';

export default function UploadDRScreen() {
  const { user } = useAuth();
  const {
    drFormData,
    updateDrFormImage,
    updateDrFormDescription,
    submitDrForm
  } = useDrForm();

  const [status, setStatus] = useState(drFormData?.statusDescription || '');
  const [image, setImage] = useState<string | null>(drFormData?.imageUrl || null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Check if user is an agent, if not redirect to dashboard
  useEffect(() => {
    if (user?.role !== 'agent') {
      router.replace('/(tabs)');
    }
  }, [user]);

  // Sync status changes with context
  useEffect(() => {
    if (status !== drFormData?.statusDescription) {
      updateDrFormDescription(status);
    }
  }, [status, drFormData?.statusDescription, updateDrFormDescription]);

  // Animation for button press
  const animatedButtonScale = useRef(new Animated.Value(1)).current;
  const successAnim = useRef(new Animated.Value(0)).current;

  const onPressIn = () => {
    Animated.spring(animatedButtonScale, {
      toValue: 0.96,
      useNativeDriver: true,
    }).start();
  };

  const onPressOut = () => {
    Animated.spring(animatedButtonScale, {
      toValue: 1,
      friction: 4,
      tension: 40,
      useNativeDriver: true,
    }).start();
  };

  const handleTakePhoto = async () => {
    // In a real app, this would use the camera
    console.log('Taking photo...');
    // For demo purposes, we'll just set the sample image
    setImage(sampleImage);

    // Update the context with the new image
    await updateDrFormImage(sampleImage);
  };

  const handleSubmit = async () => {
    if (!status.trim() || !image) {
      // Show error
      return;
    }

    setIsSubmitting(true);

    try {
      // Submit to context
      await submitDrForm(image, status);

      setIsSubmitting(false);
      setShowSuccess(true);

      // Animate success message
      Animated.sequence([
        Animated.timing(successAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.delay(1500), // Reduced delay for better UX
        Animated.timing(successAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => {
        // Reset local form state and navigate back to dashboard
        setStatus('');
        setImage(null);
        setShowSuccess(false);

        // Navigate back to dashboard after successful upload
        router.navigate("/(tabs)");
      });
    } catch (error) {
      console.error('Failed to submit DR form:', error);
      setIsSubmitting(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardView}
    >
      <ThemedView style={styles.container}>
        {/* Header */}
        <LinearGradient
          colors={[Colors.light.primary, Colors.light.secondary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <IconSymbol size={24} name="chevron.left" color="white" />
            </TouchableOpacity>
            <ThemedText style={styles.headerTitle}>DR Form Submission</ThemedText>
            <View style={{ width: 24 }} />
          </View>
        </LinearGradient>

        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          <View style={styles.formContainer}>
            <ThemedText style={styles.formTitle}>Declaration of Results Form</ThemedText>
            <ThemedText style={styles.formSubtitle}>
              Take a clear photo of the DR form and update the current status at your polling station
            </ThemedText>

            <View style={styles.card}>
              <View style={styles.inputContainer}>
                <ThemedText style={styles.inputLabel}>Current Status</ThemedText>
                <TextInput
                  style={styles.input}
                  value={status}
                  onChangeText={setStatus}
                  placeholder="Describe the current situation at your polling station"
                  placeholderTextColor={Colors.light.placeholder}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
              </View>

              <View style={styles.photoSection}>
                <ThemedText style={styles.photoLabel}>DR Form Photo</ThemedText>

                {image ? (
                  <View style={styles.imagePreviewContainer}>
                    <Image source={{ uri: image }} style={styles.imagePreview} />
                    <TouchableOpacity
                      style={styles.retakeButton}
                      onPress={() => setImage(null)}
                    >
                      <IconSymbol size={20} name="arrow.counterclockwise" color="white" />
                      <ThemedText style={styles.retakeButtonText}>Retake</ThemedText>
                    </TouchableOpacity>
                  </View>
                ) : (
                  <TouchableOpacity
                    style={styles.cameraButton}
                    onPress={handleTakePhoto}
                  >
                    <View style={styles.cameraIconContainer}>
                      <IconSymbol size={30} name="camera.fill" color={Colors.light.primary} />
                    </View>
                    <ThemedText style={styles.cameraButtonText}>
                      Take Photo of DR Form
                    </ThemedText>
                  </TouchableOpacity>
                )}
              </View>

              <Animated.View
                style={[
                  styles.buttonContainer,
                  { transform: [{ scale: animatedButtonScale }] }
                ]}
              >
                <TouchableOpacity
                  style={[
                    styles.button,
                    (!status.trim() || !image || isSubmitting) && styles.buttonDisabled
                  ]}
                  onPress={handleSubmit}
                  disabled={!status.trim() || !image || isSubmitting}
                  activeOpacity={0.9}
                  onPressIn={onPressIn}
                  onPressOut={onPressOut}
                >
                  <LinearGradient
                    colors={[Colors.light.primary, Colors.light.secondary]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.buttonGradient}
                  >
                    {isSubmitting ? (
                      <View style={styles.loadingIndicator} />
                    ) : (
                      <>
                        <IconSymbol size={20} name="arrow.up.doc.fill" color="white" style={styles.buttonIcon} />
                        <ThemedText style={styles.buttonText}>Submit DR Form</ThemedText>
                      </>
                    )}
                  </LinearGradient>
                </TouchableOpacity>
              </Animated.View>
            </View>
          </View>
        </ScrollView>

        {/* Success Message */}
        {showSuccess && (
          <Animated.View
            style={[
              styles.successMessage,
              {
                opacity: successAnim,
                transform: [
                  {
                    translateY: successAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [20, 0]
                    })
                  }
                ]
              }
            ]}
          >
            <View style={styles.successIcon}>
              <IconSymbol size={24} name="checkmark" color="white" />
            </View>
            <ThemedText style={styles.successText}>
              DR Form uploaded successfully!
            </ThemedText>
          </Animated.View>
        )}
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  keyboardView: {
    flex: 1,
    backgroundColor: Colors.light.neutral[50],
  },
  container: {
    flex: 1,
    backgroundColor: Colors.light.neutral[50],
  },
  header: {
    height: 110,
    paddingTop: 50,
    paddingHorizontal: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 40,
  },
  formContainer: {
    padding: 20,
  },
  formTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.neutral[800],
    marginBottom: 8,
  },
  formSubtitle: {
    fontSize: 16,
    color: Colors.light.neutral[500],
    marginBottom: 24,
    lineHeight: 22,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.neutral[700],
    marginBottom: 8,
  },
  input: {
    minHeight: 100,
    borderWidth: 1.5,
    borderColor: Colors.light.neutral[200],
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: Colors.light.neutral[50],
    color: Colors.light.neutral[800],
  },
  photoSection: {
    marginBottom: 24,
  },
  photoLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.neutral[700],
    marginBottom: 12,
  },
  cameraButton: {
    borderWidth: 1.5,
    borderColor: Colors.light.neutral[200],
    borderRadius: 12,
    backgroundColor: Colors.light.neutral[50],
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    borderStyle: 'dashed',
  },
  cameraIconContainer: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: `${Colors.light.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  cameraButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.primary,
  },
  imagePreviewContainer: {
    position: 'relative',
    borderRadius: 12,
    overflow: 'hidden',
    height: 200,
  },
  imagePreview: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  retakeButton: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  retakeButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  buttonContainer: {
    marginTop: 10,
  },
  button: {
    height: 56,
    borderRadius: 12,
    overflow: 'hidden',
  },
  buttonGradient: {
    height: '100%',
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  loadingIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'white',
    borderTopColor: 'transparent',
    // Animation would be added in a real app
  },
  successMessage: {
    position: 'absolute',
    bottom: 30,
    left: 20,
    right: 20,
    backgroundColor: Colors.light.success,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  successIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  successText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
