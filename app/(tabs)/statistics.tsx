import { LinearGradient } from "expo-linear-gradient";
import React, { useState } from 'react';
import {
    Dimensions,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';

import PollingStationChart from '@/components/charts/PollingStationChart';
import VoteDistributionChart from '@/components/charts/VoteDistributionChart';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';

const { width } = Dimensions.get('window');

// Sample data for charts
const voteDistributionData = [
  { party: 'NRM', votes: 12450, color: '#FFD700' },
  { party: 'NUP', votes: 9870, color: '#FF0000' },
  { party: 'FDC', votes: 5430, color: '#0000FF' },
  { party: 'DP', votes: 2340, color: '#008000' },
  { party: 'Others', votes: 1200, color: '#808080' },
];

const pollingStationData = [
  { station: 'Station 1 - Kampala Central A', votes: 850, target: 1000 },
  { station: 'Station 2 - Kampala Central B', votes: 720, target: 800 },
  { station: 'Station 3 - Nakasero', votes: 650, target: 900 },
  { station: 'Station 4 - Kololo', votes: 920, target: 950 },
  { station: 'Station 5 - Bukoto', votes: 480, target: 700 },
  { station: 'Station 6 - Ntinda', votes: 580, target: 800 },
  { station: 'Station 7 - Kansanga', votes: 750, target: 850 },
  { station: 'Station 8 - Muyenga', votes: 680, target: 750 },
  { station: 'Station 9 - Bugolobi', votes: 820, target: 900 },
  { station: 'Station 10 - Luzira', votes: 590, target: 700 },
  { station: 'Station 11 - Kabalagala', votes: 710, target: 800 },
  { station: 'Station 12 - Makindye', votes: 640, target: 750 },
  { station: 'Station 13 - Ggaba', votes: 520, target: 650 },
  { station: 'Station 14 - Katwe', votes: 780, target: 850 },
  { station: 'Station 15 - Nsambya', votes: 690, target: 800 },
  { station: 'Station 16 - Kibuli', votes: 610, target: 700 },
  { station: 'Station 17 - Wandegeya', votes: 740, target: 800 },
  { station: 'Station 18 - Makerere', votes: 660, target: 750 },
  { station: 'Station 19 - Kawempe', votes: 580, target: 700 },
  { station: 'Station 20 - Mulago', votes: 720, target: 800 },
  { station: 'Station 21 - Bwaise', votes: 550, target: 650 },
  { station: 'Station 22 - Kyebando', votes: 630, target: 750 },
  { station: 'Station 23 - Kazo', votes: 700, target: 800 },
  { station: 'Station 24 - Nansana', votes: 590, target: 700 },
];

type TabProps = {
  title: string;
  isActive: boolean;
  onPress: () => void;
};

const Tab = ({ title, isActive, onPress }: TabProps) => (
  <TouchableOpacity
    style={[styles.tab, isActive && styles.activeTab]}
    onPress={onPress}
    activeOpacity={0.8}
  >
    <ThemedText style={[styles.tabText, isActive && styles.activeTabText]}>
      {title}
    </ThemedText>
  </TouchableOpacity>
);

export default function StatisticsScreen() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={[Colors.light.primary, Colors.light.secondary]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <ThemedText style={styles.headerTitle}>Campaign Statistics</ThemedText>
          <TouchableOpacity style={styles.refreshButton}>
            <IconSymbol size={20} name="arrow.clockwise" color="white" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Tabs */}
      <View style={styles.tabContainer}>
        <Tab
          title="Overview"
          isActive={activeTab === 'overview'}
          onPress={() => setActiveTab('overview')}
        />
        <Tab
          title="Polling Stations"
          isActive={activeTab === 'stations'}
          onPress={() => setActiveTab('stations')}
        />
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {activeTab === 'overview' && (
          <>
            <ThemedView style={styles.summaryContainer}>
              <View style={styles.summaryRow}>
                <View style={styles.summaryItem}>
                  <ThemedText style={styles.summaryValue}>31,290</ThemedText>
                  <ThemedText style={styles.summaryLabel}>Total Votes</ThemedText>
                </View>
                <View style={styles.summaryItem}>
                  <ThemedText style={styles.summaryValue}>24</ThemedText>
                  <ThemedText style={styles.summaryLabel}>Polling Stations</ThemedText>
                </View>
              </View>
              <View style={styles.summaryRow}>
                <View style={styles.summaryItem}>
                  <ThemedText style={styles.summaryValue}>20</ThemedText>
                  <ThemedText style={styles.summaryLabel}>Active Agents</ThemedText>
                </View>
                <View style={styles.summaryItem}>
                  <ThemedText style={styles.summaryValue}>50%</ThemedText>
                  <ThemedText style={styles.summaryLabel}>Forms Collected</ThemedText>
                </View>
              </View>
            </ThemedView>

            <VoteDistributionChart
              data={voteDistributionData}
              title="Vote Distribution by Party"
            />

            <PollingStationChart
              data={pollingStationData.slice(0, 4)}
              title="Top Performing Polling Stations"
            />
          </>
        )}

        {activeTab === 'stations' && (
          <>
            <ThemedView style={styles.statsHeader}>
              <ThemedText style={styles.statsTitle}>Polling Station Performance</ThemedText>
              <ThemedText style={styles.statsSubtitle}>
                Performance of all polling stations against targets
              </ThemedText>
            </ThemedView>

            <PollingStationChart
              data={pollingStationData}
              title="All Polling Stations"
            />

            <ThemedView style={styles.infoCard}>
              <View style={styles.infoHeader}>
                <IconSymbol size={20} name="info.circle.fill" color={Colors.light.info} />
                <ThemedText style={styles.infoTitle}>Performance Insights</ThemedText>
              </View>
              <ThemedText style={styles.infoText}>
                • Station 4 (Kololo) and Station 9 (Bugolobi) are your best performing stations{'\n'}
                • Station 5 (Bukoto) and Station 13 (Ggaba) are underperforming and need attention{'\n'}
                • Consider reallocating resources to boost performance in Station 6 (Ntinda)
              </ThemedText>
            </ThemedView>
          </>
        )}


      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.neutral[50],
  },
  header: {
    height: 110,
    paddingTop: 50,
    paddingHorizontal: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    paddingHorizontal: 8,
    paddingVertical: 8,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: `${Colors.light.primary}15`,
  },
  tabText: {
    fontSize: 13,
    fontWeight: '500',
    color: Colors.light.neutral[600],
  },
  activeTabText: {
    color: Colors.light.primary,
    fontWeight: '600',
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  summaryContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryItem: {
    width: '48%',
    backgroundColor: Colors.light.neutral[50],
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.primary,
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.light.neutral[600],
  },
  statsHeader: {
    marginBottom: 16,
  },
  statsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.neutral[800],
    marginBottom: 4,
  },
  statsSubtitle: {
    fontSize: 14,
    color: Colors.light.neutral[500],
  },
  infoCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.neutral[800],
    marginLeft: 8,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 22,
    color: Colors.light.neutral[700],
  },
  agentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.neutral[100],
  },
  agentRank: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  agentRankText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'white',
  },
  agentInfo: {
    flex: 1,
  },
  agentName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.neutral[800],
    marginBottom: 4,
  },
  agentStats: {
    fontSize: 14,
    color: Colors.light.neutral[600],
  },

});
