import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import { useRef, useState } from "react";
import {
  Alert,
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View
} from "react-native";

import { useAuth } from "@/context/AuthContext";

import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { Colors } from "@/constants/Colors";

const { height } = Dimensions.get('window');

export default function LoginScreen() {
  const [phoneNumber, setPhoneNumber] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const animatedButtonScale = useRef(new Animated.Value(1)).current;

  // Animation for button press
  const onPressIn = () => {
    Animated.spring(animatedButtonScale, {
      toValue: 0.96,
      useNativeDriver: true,
    }).start();
  };

  const onPressOut = () => {
    Animated.spring(animatedButtonScale, {
      toValue: 1,
      friction: 4,
      tension: 40,
      useNativeDriver: true,
    }).start();
  };

  const { login, isLoading } = useAuth();

  const handleLogin = async () => {
    if (!phoneNumber.trim()) return;

    try {
      await login(phoneNumber.trim());
    } catch (error) {
      console.error('Login failed:', error);
      // In a real app, show an error message to the user
      Alert.alert('Login Failed', 'Invalid phone number or user not found');
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardView}
    >
      <StatusBar barStyle="light-content" backgroundColor={Colors.light.primary} />
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollView}
      >
        <ThemedView style={styles.container}>
          {/* Gradient Background */}
          <LinearGradient
            colors={[Colors.light.primary, Colors.light.secondary]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.headerBackground}
          />

          {/* Decorative Elements */}
          <View style={styles.decorationCircle1} />
          <View style={styles.decorationCircle2} />

          <View style={styles.contentWrapper}>
            <View style={styles.logoContainer}>
              <LinearGradient
                colors={[Colors.light.primary, Colors.light.secondary]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.logoGradient}
              >
                <View style={styles.logoInner}>
                  <IconSymbol name="chart.bar.fill" size={40} color={Colors.light.primary} />
                </View>
              </LinearGradient>
            </View>

            <View style={styles.header}>
              <ThemedText type="title" style={styles.title}>Welcome Back</ThemedText>
              <ThemedText style={styles.subtitle}>Access your Mitally political management dashboard</ThemedText>
            </View>

            <View style={styles.formContainer}>
              <View style={styles.card}>
                <View style={styles.inputWrapper}>
                  <View style={styles.inputIconContainer}>
                    <View style={styles.inputIcon}>
                      <IconSymbol name="phone.fill" size={20} color={Colors.light.primary} />
                    </View>
                    <ThemedText style={styles.label}>Phone Number</ThemedText>
                  </View>
                  <TextInput
                    style={[
                      styles.input,
                      isFocused && styles.inputFocused,
                      phoneNumber ? styles.inputFilled : {}
                    ]}
                    placeholder="Enter your phone number"
                    value={phoneNumber}
                    onChangeText={setPhoneNumber}
                    keyboardType="phone-pad"
                    placeholderTextColor={Colors.light.placeholder}
                    onFocus={() => setIsFocused(true)}
                    onBlur={() => setIsFocused(false)}
                  />
                </View>

                <Animated.View
                  style={[
                    styles.buttonContainer,
                    { transform: [{ scale: animatedButtonScale }] }
                  ]}
                >
                  <TouchableOpacity
                    style={[styles.button, (!phoneNumber || isLoading) && styles.buttonDisabled]}
                    onPress={handleLogin}
                    disabled={!phoneNumber || isLoading}
                    activeOpacity={0.9}
                    onPressIn={onPressIn}
                    onPressOut={onPressOut}
                  >
                    <LinearGradient
                      colors={[Colors.light.primary, Colors.light.secondary]}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                      style={styles.buttonGradient}
                    >
                      {isLoading ? (
                        <View style={styles.loadingIndicator} />
                      ) : (
                        <View style={styles.buttonContent}>
                          <ThemedText style={styles.buttonText}>Continue</ThemedText>
                          <IconSymbol name="chevron.right" size={20} color="white" style={styles.buttonIcon} />
                        </View>
                      )}
                    </LinearGradient>
                  </TouchableOpacity>
                </Animated.View>
              </View>
            </View>

            {/* Quick login buttons for testing */}
            <View style={styles.testLoginContainer}>
              <ThemedText style={styles.testLoginTitle}>Quick Login for Testing:</ThemedText>
              <View style={styles.testButtonsRow}>
                <TouchableOpacity
                  style={styles.testLoginButton}
                  onPress={() => {
                    setPhoneNumber('1111');
                    setTimeout(() => handleLogin(), 100);
                  }}
                >
                  <ThemedText style={styles.testLoginText}>Hon. (1111)</ThemedText>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.testLoginButton}
                  onPress={() => {
                    setPhoneNumber('2222');
                    setTimeout(() => handleLogin(), 100);
                  }}
                >
                  <ThemedText style={styles.testLoginText}>Manager (2222)</ThemedText>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.testLoginButton}
                  onPress={() => {
                    setPhoneNumber('3333');
                    setTimeout(() => handleLogin(), 100);
                  }}
                >
                  <ThemedText style={styles.testLoginText}>Agent (3333)</ThemedText>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.footer}>
              <TouchableOpacity
                onPress={() => router.replace("/(auth)/register")}
                style={styles.linkContainer}
                activeOpacity={0.7}
              >
                <ThemedText style={styles.linkText}>Don't have an account? </ThemedText>
                <View style={styles.linkWithIcon}>
                  <ThemedText style={styles.link}>Register</ThemedText>
                  <IconSymbol name="person.fill.badge.plus" size={16} color={Colors.light.primary} style={styles.linkIcon} />
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </ThemedView>


      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  keyboardView: {
    flex: 1,
    backgroundColor: Colors.light.neutral[50],
  },
  scrollView: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    minHeight: height,
    position: 'relative',
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: height * 0.3,
    borderBottomLeftRadius: 40,
    borderBottomRightRadius: 40,
    zIndex: 0,
  },
  decorationCircle1: {
    position: 'absolute',
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    top: 60,
    right: -50,
    zIndex: 1,
  },
  decorationCircle2: {
    position: 'absolute',
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    top: 20,
    left: -40,
    zIndex: 1,
  },
  contentWrapper: {
    width: '100%',
    maxWidth: 400,
    alignSelf: 'center',
    paddingHorizontal: 24,
    paddingVertical: 40,
    zIndex: 2,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoGradient: {
    width: 90,
    height: 90,
    borderRadius: 25,
    padding: 5,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  logoInner: {
    width: '100%',
    height: '100%',
    borderRadius: 20,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 40,
    fontWeight: 'bold',
    color: Colors.light.primary,
  },
  header: {
    marginBottom: 30,
    alignItems: 'center',
  },
  title: {
    fontSize: 34,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: 0.5,
    color: Colors.light.neutral[800],
  },
  subtitle: {
    textAlign: 'center',
    color: Colors.light.neutral[500],
    fontSize: 16,
    lineHeight: 24,
    maxWidth: '90%',
  },
  formContainer: {
    marginBottom: 32,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.1,
    shadowRadius: 20,
    elevation: 5,
  },
  inputWrapper: {
    marginBottom: 24,
  },
  inputIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  inputIcon: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  inputIconText: {
    fontSize: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.neutral[700],
  },
  input: {
    height: 60,
    borderWidth: 1.5,
    borderColor: Colors.light.neutral[200],
    borderRadius: 16,
    paddingHorizontal: 20,
    fontSize: 16,
    backgroundColor: Colors.light.neutral[50],
    color: Colors.light.neutral[800],
  },
  inputFocused: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.neutral[50],
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 2,
  },
  inputFilled: {
    borderColor: Colors.light.neutral[300],
    backgroundColor: Colors.light.neutral[50],
  },
  buttonContainer: {
    marginTop: 8,
  },
  button: {
    height: 60,
    borderRadius: 16,
    overflow: 'hidden',
  },
  buttonGradient: {
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    letterSpacing: 0.5,
    marginRight: 8,
  },
  buttonIcon: {
    marginLeft: 4,
  },
  footer: {
    marginTop: 24,
  },
  linkContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8,
    paddingVertical: 8,
  },
  linkText: {
    color: Colors.light.neutral[500],
    fontSize: 16,
  },
  link: {
    color: Colors.light.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  linkWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  linkIcon: {
    marginLeft: 4,
  },
  loadingIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'white',
    borderTopColor: 'transparent',
    transform: [{ rotate: '45deg' }],
  },
  // Test login styles
  testLoginContainer: {
    marginTop: 24,
    marginBottom: 16,
    alignItems: 'center',
    backgroundColor: Colors.light.neutral[100],
    borderRadius: 16,
    padding: 16,
  },
  testLoginTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.neutral[600],
    marginBottom: 12,
  },
  testButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  testLoginButton: {
    backgroundColor: Colors.light.neutral[200],
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  testLoginText: {
    fontSize: 12,
    color: Colors.light.neutral[700],
    fontWeight: '500',
  },
});