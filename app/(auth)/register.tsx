import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import { useRef, useState } from "react";
import {
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View
} from "react-native";

import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Colors } from "@/constants/Colors";

const { height } = Dimensions.get('window');

export default function RegisterScreen() {
  const [name, setName] = useState("");
  const [party, setParty] = useState("");
  const [district, setDistrict] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [email, setEmail] = useState("");
  const [constituency, setConstituency] = useState("");
  const [step, setStep] = useState(1);
  const [isFocused, setIsFocused] = useState({
    name: false,
    party: false,
    district: false,
    constituency: false,
    phoneNumber: false,
    email: false
  });

  // Animation for button press
  const animatedButtonScale = useRef(new Animated.Value(1)).current;

  const onPressIn = () => {
    Animated.spring(animatedButtonScale, {
      toValue: 0.96,
      useNativeDriver: true,
    }).start();
  };

  const onPressOut = () => {
    Animated.spring(animatedButtonScale, {
      toValue: 1,
      friction: 4,
      tension: 40,
      useNativeDriver: true,
    }).start();
  };

  const handleRegister = () => {
    // TODO: Implement actual registration API call
    router.push({
      pathname: "/verify-otp",
      params: { phone: phoneNumber, isRegistration: "true" }
    });
  };

  const handleFocus = (field: string) => {
    setIsFocused({...isFocused, [field]: true});
  };

  const handleBlur = (field: string) => {
    setIsFocused({...isFocused, [field]: false});
  };

  const handleNextStep = () => {
    if (step === 1 && name && party && district && constituency) {
      setStep(2);
    }
  };

  const handlePrevStep = () => {
    if (step === 2) {
      setStep(1);
    }
  };

  const renderInputField = (
    label: string,
    value: string,
    setter: (text: string) => void,
    field: string,
    placeholder: string,
    keyboardType: "default" | "email-address" | "numeric" | "phone-pad" = "default",
    autoCapitalize: "none" | "sentences" | "words" | "characters" = "sentences"
  ) => (
    <View style={styles.inputWrapper}>
      <View style={styles.inputIconContainer}>
        <View style={styles.inputIcon}>
          <ThemedText style={styles.inputIconText}>
            {field === 'name' ? '👤' :
             field === 'party' ? '🏛️' :
             field === 'district' ? '🗺️' :
             field === 'constituency' ? '📍' :
             field === 'phoneNumber' ? '📱' : '✉️'}
          </ThemedText>
        </View>
        <ThemedText style={styles.label}>{label}</ThemedText>
      </View>
      <TextInput
        style={[
          styles.input,
          isFocused[field] && styles.inputFocused,
          value ? styles.inputFilled : {}
        ]}
        placeholder={placeholder}
        value={value}
        onChangeText={setter}
        keyboardType={keyboardType}
        placeholderTextColor={Colors.light.placeholder}
        autoCapitalize={autoCapitalize}
        onFocus={() => handleFocus(field)}
        onBlur={() => handleBlur(field)}
      />
    </View>
  );

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardView}
    >
      <StatusBar barStyle="light-content" backgroundColor={Colors.light.primary} />
      <ThemedView style={styles.container}>
        {/* Gradient Header */}
        <LinearGradient
          colors={[Colors.light.primary, Colors.light.secondary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.header}
        >
          <View style={styles.progressContainer}>
            <View style={styles.progressCircle}>
              <View style={[styles.progressDot, step >= 1 && styles.activeDot]} />
              <ThemedText style={[styles.stepText, step === 1 && styles.activeStepText]}>1</ThemedText>
            </View>
            <View style={[styles.progressLine, step === 2 && styles.activeLine]} />
            <View style={styles.progressCircle}>
              <View style={[styles.progressDot, step === 2 && styles.activeDot]} />
              <ThemedText style={[styles.stepText, step === 2 && styles.activeStepText]}>2</ThemedText>
            </View>
          </View>
          <ThemedText style={styles.headerTitle}>
            {step === 1 ? 'Profile Information' : 'Contact Details'}
          </ThemedText>
        </LinearGradient>

        {/* Decorative Elements */}
        <View style={styles.decorationCircle1} />
        <View style={styles.decorationCircle2} />

        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          <View style={styles.logoContainer}>
            <LinearGradient
              colors={[Colors.light.primary, Colors.light.secondary]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.logoGradient}
            >
              <View style={styles.logoInner}>
                <ThemedText style={styles.logoText}>M</ThemedText>
              </View>
            </LinearGradient>
            <ThemedText style={styles.appName}>Mitally</ThemedText>
          </View>

          <ThemedText style={styles.pageTitle}>Create Account</ThemedText>
          <ThemedText style={styles.pageSubtitle}>
            {step === 1
              ? 'Please fill in your basic information'
              : 'How can we reach you?'
            }
          </ThemedText>

          <View style={styles.card}>
            {step === 1 ? (
              <View style={styles.formContainer}>
                {renderInputField('Full Name', name, setName, 'name', 'Hon. Enter your full name')}
                {renderInputField('Party', party, setParty, 'party', 'Your political party')}
                {renderInputField('District', district, setDistrict, 'district', 'Your district')}
                {renderInputField('Constituency', constituency, setConstituency, 'constituency', 'Enter your constituency')}

                <Animated.View
                  style={[
                    styles.buttonContainer,
                    { transform: [{ scale: animatedButtonScale }] }
                  ]}
                >
                  <TouchableOpacity
                    style={[
                      styles.button,
                      (!name || !party || !district || !constituency) && styles.buttonDisabled
                    ]}
                    onPress={handleNextStep}
                    disabled={!name || !party || !district || !constituency}
                    activeOpacity={0.9}
                    onPressIn={onPressIn}
                    onPressOut={onPressOut}
                  >
                    <LinearGradient
                      colors={[Colors.light.primary, Colors.light.secondary]}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                      style={styles.buttonGradient}
                    >
                      <ThemedText style={styles.buttonText}>Continue</ThemedText>
                    </LinearGradient>
                  </TouchableOpacity>
                </Animated.View>
              </View>
            ) : (
              <View style={styles.formContainer}>
                {renderInputField('Phone Number', phoneNumber, setPhoneNumber, 'phoneNumber', 'Enter your phone number', 'phone-pad')}
                {renderInputField('Email Address', email, setEmail, 'email', 'Enter your email', 'email-address', 'none')}

                <View style={styles.buttonGroup}>
                  <TouchableOpacity
                    style={styles.backButton}
                    onPress={handlePrevStep}
                    activeOpacity={0.8}
                  >
                    <ThemedText style={styles.backButtonText}>Back</ThemedText>
                  </TouchableOpacity>

                  <Animated.View
                    style={[
                      styles.buttonContainer,
                      { flex: 1, transform: [{ scale: animatedButtonScale }] }
                    ]}
                  >
                    <TouchableOpacity
                      style={[
                        styles.button,
                        !phoneNumber && styles.buttonDisabled
                      ]}
                      onPress={handleRegister}
                      disabled={!phoneNumber}
                      activeOpacity={0.9}
                      onPressIn={onPressIn}
                      onPressOut={onPressOut}
                    >
                      <LinearGradient
                        colors={[Colors.light.primary, Colors.light.secondary]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.buttonGradient}
                      >
                        <ThemedText style={styles.buttonText}>Create Account</ThemedText>
                      </LinearGradient>
                    </TouchableOpacity>
                  </Animated.View>
                </View>
              </View>
            )}
          </View>

          <View style={styles.footer}>
            <TouchableOpacity
              onPress={() => router.replace("/(auth)/login")}
              style={styles.linkContainer}
              activeOpacity={0.7}
            >
              <ThemedText style={styles.linkText}>Already have an account? </ThemedText>
              <ThemedText style={styles.link}>Login</ThemedText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  keyboardView: {
    flex: 1,
    backgroundColor: Colors.light.neutral[50],
  },
  container: {
    flex: 1,
    backgroundColor: Colors.light.neutral[50],
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    alignItems: 'center',
    paddingHorizontal: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
    zIndex: 10,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  progressCircle: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  progressDot: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    position: 'absolute',
  },
  activeDot: {
    backgroundColor: 'white',
  },
  stepText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'rgba(255, 255, 255, 0.7)',
    zIndex: 1,
  },
  activeStepText: {
    color: Colors.light.primary,
  },
  progressLine: {
    width: 80,
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    marginHorizontal: 5,
  },
  activeLine: {
    backgroundColor: 'white',
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingBottom: 40,
    position: 'relative',
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 30,
    marginBottom: 30,
  },
  logoGradient: {
    width: 50,
    height: 50,
    borderRadius: 15,
    padding: 3,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  logoInner: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.primary,
  },
  appName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: Colors.light.primary,
    marginLeft: 12,
  },
  pageTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.light.neutral[800],
    marginBottom: 8,
  },
  pageSubtitle: {
    fontSize: 16,
    color: Colors.light.neutral[500],
    marginBottom: 24,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.1,
    shadowRadius: 20,
    elevation: 5,
    marginBottom: 20,
  },
  formContainer: {
    width: '100%',
  },
  inputWrapper: {
    marginBottom: 18,
  },
  inputIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  inputIcon: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  inputIconText: {
    fontSize: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.neutral[700],
  },
  input: {
    height: 56,
    borderWidth: 1.5,
    borderColor: Colors.light.neutral[200],
    borderRadius: 14,
    paddingHorizontal: 18,
    fontSize: 16,
    backgroundColor: Colors.light.neutral[50],
    color: Colors.light.neutral[800],
  },
  inputFocused: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.neutral[50],
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 2,
  },
  inputFilled: {
    borderColor: Colors.light.neutral[300],
    backgroundColor: Colors.light.neutral[50],
  },
  buttonContainer: {
    marginTop: 8,
  },
  button: {
    height: 56,
    borderRadius: 14,
    overflow: 'hidden',
  },
  buttonGradient: {
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  backButton: {
    width: '30%',
    height: 56,
    borderWidth: 1.5,
    borderColor: Colors.light.neutral[200],
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    backgroundColor: Colors.light.neutral[50],
  },
  backButtonText: {
    color: Colors.light.neutral[600],
    fontSize: 16,
    fontWeight: '600',
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  footer: {
    marginTop: 10,
  },
  linkContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 10,
  },
  linkText: {
    color: Colors.light.neutral[500],
    fontSize: 15,
  },
  link: {
    color: Colors.light.primary,
    fontSize: 15,
    fontWeight: '600',
  },
  decorationCircle1: {
    position: 'absolute',
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(59, 130, 246, 0.05)',
    bottom: -50,
    right: -50,
    zIndex: -1,
  },
  decorationCircle2: {
    position: 'absolute',
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(59, 130, 246, 0.07)',
    top: height * 0.4,
    left: -40,
    zIndex: -1,
  },
});