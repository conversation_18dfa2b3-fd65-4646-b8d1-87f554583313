import { LinearGradient } from "expo-linear-gradient";
import { router, useLocalSearchParams } from "expo-router";
import { useEffect, useRef, useState } from "react";
import {
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View
} from "react-native";

import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Colors } from "@/constants/Colors";

const { height } = Dimensions.get('window');

export default function VerifyOTPScreen() {
  const { phone, isRegistration } = useLocalSearchParams();
  const [otp, setOtp] = useState("");
  const [countdown, setCountdown] = useState(60);

  // Animation for button press
  const animatedButtonScale = useRef(new Animated.Value(1)).current;

  const onPressIn = () => {
    Animated.spring(animatedButtonScale, {
      toValue: 0.96,
      useNativeDriver: true,
    }).start();
  };

  const onPressOut = () => {
    Animated.spring(animatedButtonScale, {
      toValue: 1,
      friction: 4,
      tension: 40,
      useNativeDriver: true,
    }).start();
  };

  // Create refs for the input
  const inputRefs = useRef<TextInput[]>([]);
  const [otpArray, setOtpArray] = useState(['', '', '', '', '', '']);
  const [isFocused, setIsFocused] = useState([false, false, false, false, false, false]);

  const handleOtpChange = (value: string, index: number) => {
    const newOtpArray = [...otpArray];
    newOtpArray[index] = value;
    setOtpArray(newOtpArray);
    setOtp(newOtpArray.join(''));

    // Auto focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (e: any, index: number) => {
    if (e.nativeEvent.key === 'Backspace' && !otpArray[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleFocus = (index: number) => {
    const newFocusState = [...isFocused];
    newFocusState[index] = true;
    setIsFocused(newFocusState);
  };

  const handleBlur = (index: number) => {
    const newFocusState = [...isFocused];
    newFocusState[index] = false;
    setIsFocused(newFocusState);
  };

  // Format countdown to MM:SS
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => (prev > 0 ? prev - 1 : 0));
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const handleVerify = () => {
    // TODO: Implement actual OTP verification API call
    router.replace("/(tabs)");
  };

  const handleResendOTP = () => {
    // TODO: Implement resend OTP API call
    setCountdown(120); // Increased to 2 minutes
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardView}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollView}
      >
        <ThemedView style={styles.container}>
          {/* Decorative Elements */}
          <View style={styles.decorationCircle1} />
          <View style={styles.decorationCircle2} />

          <View style={styles.contentWrapper}>
            <View style={styles.logoContainer}>
              <LinearGradient
                colors={[Colors.light.primary, Colors.light.secondary]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.logoGradient}
              >
                <View style={styles.logoInner}>
                  <ThemedText style={styles.logoText}>M</ThemedText>
                </View>
              </LinearGradient>
            </View>

            <View style={styles.header}>
              <ThemedText type="title" style={styles.title}>Verification Code</ThemedText>
              <ThemedText style={styles.subtitle}>
                We've sent a verification code to{'\n'}
                <ThemedText style={styles.phoneNumber}>{phone}</ThemedText>
              </ThemedText>
            </View>

            <View style={styles.card}>
              <View style={styles.otpContainer}>
                {[0, 1, 2, 3, 4, 5].map((index) => (
                  <TextInput
                    key={index}
                    ref={(ref) => inputRefs.current[index] = ref as TextInput}
                    style={[
                      styles.otpBox,
                      isFocused[index] && styles.otpBoxFocused,
                      otpArray[index] ? styles.otpBoxFilled : {}
                    ]}
                    maxLength={1}
                    value={otpArray[index]}
                    onChangeText={(value) => handleOtpChange(value, index)}
                    onKeyPress={(e) => handleKeyPress(e, index)}
                    keyboardType="number-pad"
                    selectTextOnFocus
                    placeholder="•"
                    placeholderTextColor={Colors.light.placeholder}
                    onFocus={() => handleFocus(index)}
                    onBlur={() => handleBlur(index)}
                  />
                ))}
              </View>

              <Animated.View
                style={[
                  styles.buttonContainer,
                  { transform: [{ scale: animatedButtonScale }] }
                ]}
              >
                <TouchableOpacity
                  style={[styles.button, otp.length < 4 && styles.buttonDisabled]}
                  onPress={handleVerify}
                  disabled={otp.length < 4}
                  activeOpacity={0.9}
                  onPressIn={onPressIn}
                  onPressOut={onPressOut}
                >
                  <LinearGradient
                    colors={[Colors.light.primary, Colors.light.secondary]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.buttonGradient}
                  >
                    <ThemedText style={styles.buttonText}>Verify Code</ThemedText>
                  </LinearGradient>
                </TouchableOpacity>
              </Animated.View>

              <View style={styles.resendContainer}>
                {countdown > 0 ? (
                  <View style={styles.timerContainer}>
                    <ThemedText style={styles.resendText}>
                      Resend code in{' '}
                    </ThemedText>
                    <ThemedText style={styles.countdown}>
                      {formatTime(countdown)}
                    </ThemedText>
                  </View>
                ) : (
                  <TouchableOpacity
                    onPress={handleResendOTP}
                    style={styles.resendButton}
                  >
                    <ThemedText style={styles.resendLink}>
                      Resend Code
                    </ThemedText>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </View>
        </ThemedView>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  keyboardView: {
    flex: 1,
    backgroundColor: Colors.light.neutral[50],
  },
  scrollView: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  container: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'center',
    minHeight: height,
    backgroundColor: Colors.light.neutral[50],
    position: 'relative',
  },
  contentWrapper: {
    width: '100%',
    maxWidth: 400,
    alignSelf: 'center',
    paddingVertical: 40,
    zIndex: 2,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoGradient: {
    width: 90,
    height: 90,
    borderRadius: 25,
    padding: 5,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  logoInner: {
    width: '100%',
    height: '100%',
    borderRadius: 20,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 40,
    fontWeight: 'bold',
    color: Colors.light.primary,
  },
  header: {
    marginBottom: 30,
    alignItems: 'center',
  },
  title: {
    fontSize: 34,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: 0.5,
    color: Colors.light.neutral[800],
  },
  subtitle: {
    textAlign: 'center',
    color: Colors.light.neutral[500],
    fontSize: 16,
    lineHeight: 24,
  },
  phoneNumber: {
    color: Colors.light.neutral[700],
    fontWeight: '600',
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.1,
    shadowRadius: 20,
    elevation: 5,
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  otpBox: {
    width: 50,
    height: 60,
    borderRadius: 14,
    borderWidth: 1.5,
    borderColor: Colors.light.neutral[200],
    backgroundColor: Colors.light.neutral[50],
    fontSize: 24,
    textAlign: 'center',
    color: Colors.light.neutral[800],
    fontWeight: '600',
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  otpBoxFocused: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.neutral[50],
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 2,
  },
  otpBoxFilled: {
    borderColor: Colors.light.neutral[300],
    backgroundColor: Colors.light.neutral[50],
  },
  buttonContainer: {
    marginTop: 8,
  },
  button: {
    height: 60,
    borderRadius: 16,
    overflow: 'hidden',
  },
  buttonGradient: {
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  resendContainer: {
    alignItems: 'center',
    marginTop: 24,
  },
  timerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  resendButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  resendText: {
    color: Colors.light.neutral[500],
    fontSize: 15,
  },
  countdown: {
    color: Colors.light.primary,
    fontSize: 15,
    fontWeight: '600',
    minWidth: 45,
    textAlign: 'center',
  },
  resendLink: {
    color: Colors.light.primary,
    fontSize: 15,
    fontWeight: '600',
  },
  decorationCircle1: {
    position: 'absolute',
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(59, 130, 246, 0.05)',
    bottom: -50,
    right: -50,
    zIndex: 0,
  },
  decorationCircle2: {
    position: 'absolute',
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(59, 130, 246, 0.07)',
    top: height * 0.2,
    left: -40,
    zIndex: 0,
  },
});