import { router } from "expo-router";
import { useEffect } from "react";

export default function Index() {
  useEffect(() => {
    // Use a small timeout to ensure the root layout is fully mounted
    const timer = setTimeout(() => {
      // Check if user is authenticated
      const isAuthenticated = true;

      if (isAuthenticated) {
        router.replace("/(tabs)");
      } else {
        router.replace("/(auth)/login");
      }
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // This screen won't be visible as we're redirecting
  return null;
}
