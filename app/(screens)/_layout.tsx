import { Stack } from 'expo-router';
import { Platform } from 'react-native';

export default function ScreensLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        animation: Platform.OS === 'android' ? 'fade' : 'default',
        animationDuration: 200,
        presentation: 'card',
        contentStyle: { backgroundColor: 'white' },
        // These options help prevent the "web-like" refresh behavior
        unmountOnBlur: false, // Keep screens mounted when switching
        freezeOnBlur: true,   // Preserve state when not focused
      }}
    />
  );
}
