import { LinearGradient } from "expo-linear-gradient";
import { router, useLocalSearchParams } from "expo-router";
import React, { useState } from 'react';
import {
    Dimensions,
    Image,
    Modal,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';

import ScreenWrapper from '@/components/ScreenWrapper';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';

const { width, height } = Dimensions.get('window');

// Sample data for DR forms
const SAMPLE_FORMS = {
  '1': {
    id: '1',
    station: 'Kampala Central A',
    agent: '<PERSON>',
    timestamp: '2023-08-15 09:30 AM',
    status: 'verified',
    imageUrl: 'https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dm90aW5nfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
    votes: {
      candidate1: 450,
      candidate2: 320,
      candidate3: 180,
      candidate4: 50,
    },
    totalVotes: 1000,
    notes: 'Form was submitted on time. No irregularities reported.',
    verifiedBy: 'Campaign Manager',
    verifiedAt: '2023-08-15 11:45 AM',
  },
  '2': {
    id: '2',
    station: 'Kampala Central B',
    agent: 'Jane Smith',
    timestamp: '2023-08-15 10:15 AM',
    status: 'verified',
    imageUrl: 'https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dm90aW5nfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
    votes: {
      candidate1: 380,
      candidate2: 290,
      candidate3: 150,
      candidate4: 80,
    },
    totalVotes: 900,
    notes: 'Form was submitted on time. Minor discrepancy in total count, but within acceptable range.',
    verifiedBy: 'Campaign Manager',
    verifiedAt: '2023-08-15 12:30 PM',
  },
  '3': {
    id: '3',
    station: 'Nakasero',
    agent: 'Robert Johnson',
    timestamp: '2023-08-15 11:45 AM',
    status: 'pending',
    imageUrl: 'https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dm90aW5nfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
    votes: {
      candidate1: 410,
      candidate2: 350,
      candidate3: 190,
      candidate4: 70,
    },
    totalVotes: 1020,
    notes: 'Awaiting verification. Image quality is good.',
    verifiedBy: null,
    verifiedAt: null,
  },
};

export default function FormDetailsScreen() {
  const { id } = useLocalSearchParams();
  const formId = typeof id === 'string' ? id : '1';
  const form = SAMPLE_FORMS[formId as keyof typeof SAMPLE_FORMS] || SAMPLE_FORMS['1'];

  const [imageExpanded, setImageExpanded] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);

  const toggleImageExpand = () => {
    setImageExpanded(!imageExpanded);
  };

  const openImageModal = () => {
    setModalVisible(true);
  };

  const closeImageModal = () => {
    setModalVisible(false);
  };

  return (
    <ScreenWrapper backgroundColor={Colors.light.neutral[50]}>
      <ThemedView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={[Colors.light.primary, Colors.light.secondary]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol size={24} name="chevron.left" color="white" />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>DR Form Details</ThemedText>
          <View style={{ width: 24 }} />
        </View>
      </LinearGradient>

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Status Badge */}
        <View style={[
          styles.statusBadge,
          { backgroundColor: form.status === 'verified' ? Colors.light.success : Colors.light.warning }
        ]}>
          <IconSymbol
            size={16}
            name={form.status === 'verified' ? 'checkmark.circle.fill' : 'clock.fill'}
            color="white"
          />
          <ThemedText style={styles.statusText}>
            {form.status === 'verified' ? 'Verified' : 'Pending Verification'}
          </ThemedText>
        </View>

        {/* Form Image */}
        <TouchableOpacity
          style={styles.imageContainer}
          onPress={openImageModal}
          activeOpacity={0.9}
        >
          <Image
            source={{ uri: form.imageUrl }}
            style={[
              styles.formImage,
              imageExpanded && styles.expandedImage
            ]}
            resizeMode="contain"
          />
          <View style={styles.zoomIndicator}>
            <IconSymbol size={16} name="plus.circle.fill" color="white" />
            <ThemedText style={styles.zoomText}>View Full Screen</ThemedText>
          </View>
        </TouchableOpacity>

        {/* Full Screen Image Modal */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={modalVisible}
          onRequestClose={closeImageModal}
        >
          <View style={styles.modalContainer}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={closeImageModal}
            >
              <IconSymbol size={24} name="xmark.circle.fill" color="white" />
            </TouchableOpacity>

            <View style={styles.modalImageContainer}>
              <Image
                source={{ uri: form.imageUrl }}
                style={styles.modalImage}
                resizeMode="contain"
              />
            </View>

            <View style={styles.modalControls}>
              <TouchableOpacity style={styles.modalControlButton}>
                <IconSymbol size={20} name="arrow.counterclockwise" color="white" />
                <ThemedText style={styles.modalControlText}>Rotate</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity style={styles.modalControlButton}>
                <IconSymbol size={20} name="plus.circle.fill" color="white" />
                <ThemedText style={styles.modalControlText}>Zoom In</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity style={styles.modalControlButton}>
                <IconSymbol size={20} name="minus.circle.fill" color="white" />
                <ThemedText style={styles.modalControlText}>Zoom Out</ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>

        {/* Form Details */}
        <ThemedView style={styles.detailsCard}>
          <ThemedText style={styles.sectionTitle}>Polling Station Information</ThemedText>

          <View style={styles.detailRow}>
            <View style={styles.detailIconContainer}>
              <IconSymbol size={16} name="building.2.fill" color={Colors.light.primary} />
            </View>
            <View style={styles.detailTextContainer}>
              <ThemedText style={styles.detailLabel}>Station</ThemedText>
              <ThemedText style={styles.detailValue}>{form.station}</ThemedText>
            </View>
          </View>

          <View style={styles.detailRow}>
            <View style={styles.detailIconContainer}>
              <IconSymbol size={16} name="person.fill" color={Colors.light.primary} />
            </View>
            <View style={styles.detailTextContainer}>
              <ThemedText style={styles.detailLabel}>Submitted By</ThemedText>
              <ThemedText style={styles.detailValue}>{form.agent}</ThemedText>
            </View>
          </View>

          <View style={styles.detailRow}>
            <View style={styles.detailIconContainer}>
              <IconSymbol size={16} name="calendar" color={Colors.light.primary} />
            </View>
            <View style={styles.detailTextContainer}>
              <ThemedText style={styles.detailLabel}>Submission Time</ThemedText>
              <ThemedText style={styles.detailValue}>{form.timestamp}</ThemedText>
            </View>
          </View>

          {form.status === 'verified' && (
            <>
              <View style={styles.detailRow}>
                <View style={styles.detailIconContainer}>
                  <IconSymbol size={16} name="person.crop.circle.fill" color={Colors.light.primary} />
                </View>
                <View style={styles.detailTextContainer}>
                  <ThemedText style={styles.detailLabel}>Verified By</ThemedText>
                  <ThemedText style={styles.detailValue}>{form.verifiedBy}</ThemedText>
                </View>
              </View>

              <View style={styles.detailRow}>
                <View style={styles.detailIconContainer}>
                  <IconSymbol size={16} name="clock.fill" color={Colors.light.primary} />
                </View>
                <View style={styles.detailTextContainer}>
                  <ThemedText style={styles.detailLabel}>Verification Time</ThemedText>
                  <ThemedText style={styles.detailValue}>{form.verifiedAt}</ThemedText>
                </View>
              </View>
            </>
          )}
        </ThemedView>

        {/* Vote Counts */}
        <ThemedView style={styles.detailsCard}>
          <ThemedText style={styles.sectionTitle}>Vote Counts</ThemedText>

          <View style={styles.voteRow}>
            <ThemedText style={styles.candidateName}>Candidate 1</ThemedText>
            <View style={styles.voteBarContainer}>
              <View
                style={[
                  styles.voteBar,
                  {
                    width: `${(form.votes.candidate1 / form.totalVotes) * 100}%`,
                    backgroundColor: Colors.light.primary
                  }
                ]}
              />
            </View>
            <ThemedText style={styles.voteCount}>{form.votes.candidate1}</ThemedText>
          </View>

          <View style={styles.voteRow}>
            <ThemedText style={styles.candidateName}>Candidate 2</ThemedText>
            <View style={styles.voteBarContainer}>
              <View
                style={[
                  styles.voteBar,
                  {
                    width: `${(form.votes.candidate2 / form.totalVotes) * 100}%`,
                    backgroundColor: '#F59E0B'
                  }
                ]}
              />
            </View>
            <ThemedText style={styles.voteCount}>{form.votes.candidate2}</ThemedText>
          </View>

          <View style={styles.voteRow}>
            <ThemedText style={styles.candidateName}>Candidate 3</ThemedText>
            <View style={styles.voteBarContainer}>
              <View
                style={[
                  styles.voteBar,
                  {
                    width: `${(form.votes.candidate3 / form.totalVotes) * 100}%`,
                    backgroundColor: '#10B981'
                  }
                ]}
              />
            </View>
            <ThemedText style={styles.voteCount}>{form.votes.candidate3}</ThemedText>
          </View>

          <View style={styles.voteRow}>
            <ThemedText style={styles.candidateName}>Candidate 4</ThemedText>
            <View style={styles.voteBarContainer}>
              <View
                style={[
                  styles.voteBar,
                  {
                    width: `${(form.votes.candidate4 / form.totalVotes) * 100}%`,
                    backgroundColor: '#8B5CF6'
                  }
                ]}
              />
            </View>
            <ThemedText style={styles.voteCount}>{form.votes.candidate4}</ThemedText>
          </View>

          <View style={styles.totalVotesContainer}>
            <ThemedText style={styles.totalVotesLabel}>Total Votes:</ThemedText>
            <ThemedText style={styles.totalVotesValue}>{form.totalVotes}</ThemedText>
          </View>
        </ThemedView>

        {/* Notes */}
        <ThemedView style={styles.detailsCard}>
          <ThemedText style={styles.sectionTitle}>Notes</ThemedText>
          <ThemedText style={styles.notesText}>{form.notes}</ThemedText>
        </ThemedView>
      </ScrollView>
    </ThemedView>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.neutral[50],
  },
  header: {
    height: 110,
    paddingTop: 50,
    paddingHorizontal: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginBottom: 16,
  },
  statusText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  imageContainer: {
    position: 'relative',
    backgroundColor: 'black',
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
  },
  formImage: {
    width: '100%',
    height: 250,
  },
  expandedImage: {
    height: 400,
  },
  zoomIndicator: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 20,
  },
  zoomText: {
    color: 'white',
    fontSize: 12,
    marginLeft: 4,
  },
  detailsCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.neutral[800],
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: `${Colors.light.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  detailTextContainer: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    color: Colors.light.neutral[500],
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.neutral[800],
  },
  voteRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  candidateName: {
    width: 100,
    fontSize: 14,
    color: Colors.light.neutral[700],
  },
  voteBarContainer: {
    flex: 1,
    height: 12,
    backgroundColor: Colors.light.neutral[100],
    borderRadius: 6,
    marginHorizontal: 12,
    overflow: 'hidden',
  },
  voteBar: {
    height: '100%',
    borderRadius: 6,
  },
  voteCount: {
    width: 50,
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.neutral[800],
    textAlign: 'right',
  },
  totalVotesContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.light.neutral[100],
  },
  totalVotesLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.neutral[700],
    marginRight: 8,
  },
  totalVotesValue: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.neutral[800],
  },
  notesText: {
    fontSize: 16,
    lineHeight: 24,
    color: Colors.light.neutral[700],
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 10,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalImageContainer: {
    width: width,
    height: height * 0.7,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalImage: {
    width: width,
    height: height * 0.7,
  },
  modalControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
  },
  modalControlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 25,
    marginHorizontal: 8,
  },
  modalControlText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
});
