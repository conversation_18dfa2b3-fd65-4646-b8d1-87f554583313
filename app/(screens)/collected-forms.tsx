import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import React, { useState } from 'react';
import {
    Dimensions,
    Image,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';

import ScreenWrapper from '@/components/ScreenWrapper';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';

const { width } = Dimensions.get('window');

// Sample data for DR forms (12 total: 10 verified + 2 pending)
const SAMPLE_FORMS = [
  {
    id: '1',
    station: 'Station 1 - Kampala Central A',
    agent: '<PERSON>',
    timestamp: '2023-08-15 09:30 AM',
    status: 'verified',
    imageUrl: 'https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dm90aW5nfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
  },
  {
    id: '2',
    station: 'Station 2 - Kampala Central B',
    agent: 'Jane Smith',
    timestamp: '2023-08-15 10:15 AM',
    status: 'verified',
    imageUrl: 'https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dm90aW5nfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
  },
  {
    id: '3',
    station: 'Station 3 - Nakasero',
    agent: 'Robert Johnson',
    timestamp: '2023-08-15 11:45 AM',
    status: 'pending',
    imageUrl: 'https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dm90aW5nfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
  },
  {
    id: '4',
    station: 'Station 4 - Kololo',
    agent: 'Mary Williams',
    timestamp: '2023-08-15 12:30 PM',
    status: 'verified',
    imageUrl: 'https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dm90aW5nfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
  },
  {
    id: '5',
    station: 'Station 5 - Bukoto',
    agent: 'David Brown',
    timestamp: '2023-08-15 01:15 PM',
    status: 'verified',
    imageUrl: 'https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dm90aW5nfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
  },
  {
    id: '6',
    station: 'Station 6 - Ntinda',
    agent: 'Sarah Wilson',
    timestamp: '2023-08-15 02:00 PM',
    status: 'verified',
    imageUrl: 'https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dm90aW5nfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
  },
  {
    id: '7',
    station: 'Station 7 - Kansanga',
    agent: 'Michael Davis',
    timestamp: '2023-08-15 02:30 PM',
    status: 'verified',
    imageUrl: 'https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dm90aW5nfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
  },
  {
    id: '8',
    station: 'Station 8 - Muyenga',
    agent: 'Lisa Anderson',
    timestamp: '2023-08-15 03:00 PM',
    status: 'verified',
    imageUrl: 'https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dm90aW5nfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
  },
  {
    id: '9',
    station: 'Station 9 - Bugolobi',
    agent: 'James Taylor',
    timestamp: '2023-08-15 03:30 PM',
    status: 'verified',
    imageUrl: 'https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dm90aW5nfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
  },
  {
    id: '10',
    station: 'Station 10 - Luzira',
    agent: 'Emma Thompson',
    timestamp: '2023-08-15 04:00 PM',
    status: 'verified',
    imageUrl: 'https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dm90aW5nfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
  },
  {
    id: '11',
    station: 'Station 11 - Kabalagala',
    agent: 'Peter Clark',
    timestamp: '2023-08-15 04:30 PM',
    status: 'pending',
    imageUrl: 'https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dm90aW5nfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
  },
  {
    id: '12',
    station: 'Station 12 - Makindye',
    agent: 'Grace Miller',
    timestamp: '2023-08-15 05:00 PM',
    status: 'verified',
    imageUrl: 'https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dm90aW5nfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
  },
];

type FormItemProps = {
  form: {
    id: string;
    station: string;
    agent: string;
    timestamp: string;
    status: 'verified' | 'pending';
    imageUrl: string;
  };
  onPress: () => void;
};

const FormItem = ({ form, onPress }: FormItemProps) => {
  return (
    <TouchableOpacity
      style={styles.formItem}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.formImageContainer}>
        <Image source={{ uri: form.imageUrl }} style={styles.formImage} />
        <View style={[
          styles.statusBadge,
          { backgroundColor: form.status === 'verified' ? Colors.light.success : Colors.light.warning }
        ]}>
          <IconSymbol
            size={12}
            name={form.status === 'verified' ? 'checkmark.circle.fill' : 'clock.fill'}
            color="white"
          />
          <ThemedText style={styles.statusText}>
            {form.status === 'verified' ? 'Verified' : 'Pending'}
          </ThemedText>
        </View>
      </View>

      <View style={styles.formDetails}>
        <ThemedText style={styles.stationName}>{form.station}</ThemedText>
        <View style={styles.formInfoRow}>
          <IconSymbol size={14} name="person.fill" color={Colors.light.neutral[500]} />
          <ThemedText style={styles.formInfoText}>{form.agent}</ThemedText>
        </View>
        <View style={styles.formInfoRow}>
          <IconSymbol size={14} name="calendar" color={Colors.light.neutral[500]} />
          <ThemedText style={styles.formInfoText}>{form.timestamp}</ThemedText>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default function CollectedFormsScreen() {
  const [selectedForm, setSelectedForm] = useState<string | null>(null);

  const handleFormPress = (formId: string) => {
    setSelectedForm(formId);
    // Use navigate instead of push for consistent transitions
    router.navigate({
      pathname: "/(screens)/form-details",
      params: { id: formId }
    });
  };

  return (
    <ScreenWrapper backgroundColor={Colors.light.neutral[50]}>
      <ThemedView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={[Colors.light.primary, Colors.light.secondary]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol size={24} name="chevron.left" color="white" />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>Collected DR Forms</ThemedText>
          <View style={{ width: 24 }} />
        </View>
      </LinearGradient>

      {/* Stats Summary */}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <ThemedText style={styles.statValue}>12</ThemedText>
          <ThemedText style={styles.statLabel}>Total Forms</ThemedText>
        </View>
        <View style={[styles.statItem, styles.statDivider]}>
          <ThemedText style={styles.statValue}>10</ThemedText>
          <ThemedText style={styles.statLabel}>Verified</ThemedText>
        </View>
        <View style={styles.statItem}>
          <ThemedText style={styles.statValue}>2</ThemedText>
          <ThemedText style={styles.statLabel}>Pending</ThemedText>
        </View>
      </View>

      {/* Forms List */}
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {SAMPLE_FORMS.map((form) => (
          <FormItem
            key={form.id}
            form={form}
            onPress={() => handleFormPress(form.id)}
          />
        ))}
      </ScrollView>
    </ThemedView>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.neutral[50],
  },
  header: {
    height: 110,
    paddingTop: 50,
    paddingHorizontal: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginTop: -20,
    borderRadius: 16,
    padding: 16,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statDivider: {
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderColor: Colors.light.neutral[100],
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: Colors.light.neutral[500],
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  formItem: {
    backgroundColor: 'white',
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    overflow: 'hidden',
  },
  formImageContainer: {
    position: 'relative',
    height: 180,
  },
  formImage: {
    width: '100%',
    height: '100%',
  },
  statusBadge: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 20,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  formDetails: {
    padding: 16,
  },
  stationName: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.neutral[800],
    marginBottom: 8,
  },
  formInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  formInfoText: {
    fontSize: 14,
    color: Colors.light.neutral[600],
    marginLeft: 8,
  },
});
