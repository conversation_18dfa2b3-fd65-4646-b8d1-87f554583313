import { SplashScreen, Stack, usePathname, useSegments } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect, useState } from 'react';
import { Platform } from 'react-native';

import { AuthGuard } from '@/components/AuthGuard';
import PreloadScreen from '@/components/PreloadScreen';
import { AuthProvider } from '@/context/AuthContext';

// Prevent the splash screen from auto-hiding
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [isReady, setIsReady] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const segments = useSegments();
  const pathname = usePathname();

  // Track navigation state changes with optimized timing
  useEffect(() => {
    // When the segments or pathname changes, briefly show the loading state
    // Use a shorter delay to match our animation durations
    setIsNavigating(true);

    // Use a shorter delay that matches our animation durations (150ms)
    const timer = setTimeout(() => {
      setIsNavigating(false);
    }, 150);

    return () => clearTimeout(timer);
  }, [segments, pathname]);

  useEffect(() => {
    // Perform any initialization tasks here
    const prepare = async () => {
      try {
        // Add any async initialization here (like loading fonts, etc.)
        await new Promise(resolve => setTimeout(resolve, 500)); // Artificial delay for demo
      } catch (e) {
        console.warn(e);
      } finally {
        // Tell the application to render
        setIsReady(true);
        SplashScreen.hideAsync();
      }
    };

    prepare();
  }, []);

  if (!isReady) {
    return null;
  }

  return (
    <AuthProvider>
      {/* AuthGuard handles redirects based on authentication state */}
      <AuthGuard>
        <StatusBar style="auto" />
        <Stack
          initialRouteName="index"
          screenOptions={{
            headerShown: false,
            animation: Platform.OS === 'android' ? 'fade' : 'default',
            animationDuration: 200,
            presentation: 'card',
            contentStyle: { backgroundColor: 'white' },
            // This is important - it prevents screens from remounting
            unmountOnBlur: false,
          }}
        >
          <Stack.Screen
            name="(auth)"
            options={{
              headerShown: false,
              // Preserve state when navigating away
              freezeOnBlur: true,
            }}
          />
          <Stack.Screen
            name="(tabs)"
            options={{
              headerShown: false,
              // Preserve state when navigating away
              freezeOnBlur: true,
            }}
          />
          <Stack.Screen
            name="(screens)"
            options={{
              headerShown: false,
              // Preserve state when navigating away
              freezeOnBlur: true,
            }}
          />
        </Stack>

        {/* Preload screen to prevent white flashes during transitions */}
        <PreloadScreen visible={isNavigating} showSpinner={true} />
      </AuthGuard>
    </AuthProvider>
  );
}
