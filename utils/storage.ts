import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys
export const STORAGE_KEYS = {
  USER: '@mitally_user',
  SESSION: '@mitally_session',
  SETTINGS: '@mitally_settings',
} as const;

// Generic storage utilities
export const storage = {
  // Get item from storage
  async getItem<T>(key: string): Promise<T | null> {
    try {
      const value = await AsyncStorage.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error(`Failed to get item ${key} from storage:`, error);
      return null;
    }
  },

  // Set item in storage
  async setItem<T>(key: string, value: T): Promise<boolean> {
    try {
      await AsyncStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error(`Failed to set item ${key} in storage:`, error);
      return false;
    }
  },

  // Remove item from storage
  async removeItem(key: string): Promise<boolean> {
    try {
      await AsyncStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error(`Failed to remove item ${key} from storage:`, error);
      return false;
    }
  },

  // Clear all storage
  async clear(): Promise<boolean> {
    try {
      await AsyncStorage.clear();
      return true;
    } catch (error) {
      console.error('Failed to clear storage:', error);
      return false;
    }
  },

  // Get all keys
  async getAllKeys(): Promise<string[]> {
    try {
      return await AsyncStorage.getAllKeys();
    } catch (error) {
      console.error('Failed to get all keys from storage:', error);
      return [];
    }
  },
};

// Auth-specific storage utilities
export const authStorage = {
  // Save user session
  async saveSession(user: any): Promise<boolean> {
    const userSaved = await storage.setItem(STORAGE_KEYS.USER, user);
    const sessionSaved = await storage.setItem(STORAGE_KEYS.SESSION, 'active');
    return userSaved && sessionSaved;
  },

  // Load user session
  async loadSession(): Promise<any | null> {
    const sessionStatus = await storage.getItem<string>(STORAGE_KEYS.SESSION);
    if (sessionStatus !== 'active') {
      return null;
    }
    return await storage.getItem(STORAGE_KEYS.USER);
  },

  // Clear user session
  async clearSession(): Promise<boolean> {
    const userRemoved = await storage.removeItem(STORAGE_KEYS.USER);
    const sessionRemoved = await storage.removeItem(STORAGE_KEYS.SESSION);
    return userRemoved && sessionRemoved;
  },

  // Check if session exists
  async hasActiveSession(): Promise<boolean> {
    const sessionStatus = await storage.getItem<string>(STORAGE_KEYS.SESSION);
    return sessionStatus === 'active';
  },
};
