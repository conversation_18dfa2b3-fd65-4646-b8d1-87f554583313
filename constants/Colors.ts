
// Primary brand colors
const primaryLight = '#3B5BDB'; // Deeper blue for primary actions
const primaryDark = '#5C7CFA';
const secondaryLight = '#4C6EF5'; // Slightly lighter blue for secondary elements
const secondaryDark = '#748FFC';

// Accent colors
const accentLight = '#22B8CF'; // Teal accent for highlights
const accentDark = '#3BC9DB';

// Neutral colors
const neutralLight = {
  50: '#F8FAFC',  // Lightest background
  100: '#F1F5F9', // Light background
  200: '#E2E8F0', // Borders, dividers
  300: '#CBD5E1', // Disabled state
  400: '#94A3B8', // Placeholder text
  500: '#64748B', // Secondary text
  600: '#475569', // Primary text (light mode)
  700: '#334155', // Headings (light mode)
  800: '#1E293B', // Dark text
  900: '#0F172A', // Darkest text
};

const neutralDark = {
  50: '#1A1A1A',  // Darkest background
  100: '#262626', // Dark background
  200: '#333333', // Borders, dividers
  300: '#404040', // Disabled state
  400: '#6B7280', // Placeholder text
  500: '#9CA3AF', // Secondary text
  600: '#D1D5DB', // Primary text (dark mode)
  700: '#E5E7EB', // Headings (dark mode)
  800: '#F3F4F6', // Light text
  900: '#FFFFFF', // Lightest text
};

export const Colors = {
  light: {
    text: neutralLight[700],
    background: neutralLight[50],
    tint: primaryLight,
    primary: primaryLight,
    secondary: secondaryLight,
    accent: accentLight,
    tabIconDefault: neutralLight[300],
    tabIconSelected: primaryLight,
    cardBackground: neutralLight[100],
    border: neutralLight[200],
    placeholder: neutralLight[400],
    secondaryText: neutralLight[500],
    error: '#E53E3E',
    success: '#10B981',
    warning: '#F59E0B',
    info: '#3B82F6',
    neutral: neutralLight,
  },
  dark: {
    text: neutralDark[700],
    background: neutralDark[50],
    tint: primaryDark,
    primary: primaryDark,
    secondary: secondaryDark,
    accent: accentDark,
    tabIconDefault: neutralDark[400],
    tabIconSelected: primaryDark,
    cardBackground: neutralDark[100],
    border: neutralDark[200],
    placeholder: neutralDark[400],
    secondaryText: neutralDark[500],
    error: '#F87171',
    success: '#34D399',
    warning: '#FBBF24',
    info: '#60A5FA',
    neutral: neutralDark,
  },
};

