import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useEffect, useRef } from 'react';
import { Animated, StyleSheet, View } from 'react-native';

interface ScreenWrapperProps {
  children: React.ReactNode;
  backgroundColor?: string;
}

/**
 * A wrapper component for screens to prevent white flashes during transitions
 * and provide a more native-like experience
 */
export default function ScreenWrapper({
  children,
  backgroundColor
}: ScreenWrapperProps) {
  const colorScheme = useColorScheme();
  const bgColor = backgroundColor ||
    (colorScheme === 'dark' ? Colors.dark.background : Colors.light.background);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(10)).current;

  useEffect(() => {
    // Start animations when component mounts - use the same duration for both
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: 0,
        duration: 150, // Match the fade duration for more consistent animation
        useNativeDriver: true,
      })
    ]).start();

    return () => {
      // Reset animations when component unmounts
      fadeAnim.setValue(0);
      translateY.setValue(10);
    };
  }, [fadeAnim, translateY]); // Add dependencies to avoid lint warnings

  return (
    <View style={[styles.container, { backgroundColor: bgColor }]}>
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY }]
          }
        ]}
      >
        {children}
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  }
});
