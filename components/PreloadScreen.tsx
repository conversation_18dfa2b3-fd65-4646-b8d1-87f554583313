import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useEffect } from 'react';
import { ActivityIndicator, Animated, StyleSheet, View } from 'react-native';

interface ScreenTransitionProps {
  children: React.ReactNode;
}

/**
 * A wrapper component that prevents white flashes during screen transitions
 * by maintaining a consistent background color
 */
export function ScreenTransition({ children }: ScreenTransitionProps) {
  const colorScheme = useColorScheme();
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const bgColor = colorScheme === 'dark' ? Colors.dark.background : Colors.light.background;

  useEffect(() => {
    // Fade in the content when mounted
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 150,
      useNativeDriver: true,
    }).start();

    return () => {
      // This will run on unmount but won't be visible
      // since the component will already be removed from the tree
      fadeAnim.setValue(0);
    };
  }, []);

  return (
    <View style={[styles.container, { backgroundColor: bgColor }]}>
      <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
        {children}
      </Animated.View>
    </View>
  );
}

interface PreloadScreenProps {
  visible: boolean;
  backgroundColor?: string;
  showSpinner?: boolean;
}

/**
 * A component that shows a fade-in/fade-out screen during transitions
 * to prevent white flashes
 */
export default function PreloadScreen({
  visible,
  backgroundColor,
  showSpinner = false
}: PreloadScreenProps) {
  const colorScheme = useColorScheme();
  const fadeAnim = React.useRef(new Animated.Value(0)).current;

  const bgColor = backgroundColor ||
    (colorScheme === 'dark' ? Colors.dark.background : Colors.light.background);

  useEffect(() => {
    if (visible) {
      // Fade in with a short duration for smoother appearance
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 50, // Short but not immediate for smoother transition
        useNativeDriver: true,
      }).start();
    } else {
      // Fade out with matching duration
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 150, // Shorter to match other animations
        useNativeDriver: true,
      }).start();
    }
  }, [visible, fadeAnim]);

  if (!visible && fadeAnim._value === 0) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: bgColor,
          opacity: fadeAnim,
          // If not visible, move to back
          zIndex: visible ? 999 : -1,
        }
      ]}
      pointerEvents={visible ? 'auto' : 'none'}
    >
      {showSpinner && visible && (
        <ActivityIndicator size="large" color={Colors.light.primary} />
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    width: '100%',
  },
});
