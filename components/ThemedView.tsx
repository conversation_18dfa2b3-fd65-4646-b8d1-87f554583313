
import React from 'react';
import { View, ViewProps } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';

interface ThemedViewProps extends ViewProps {
  lightBg?: string;
  darkBg?: string;
}

export function ThemedView({
  style,
  lightBg,
  darkBg,
  ...props
}: ThemedViewProps) {
  const colorScheme = useColorScheme();
  
  const backgroundColor = colorScheme === 'dark' 
    ? darkBg || Colors.dark.background 
    : lightBg || Colors.light.background;

  return (
    <View
      style={[
        { backgroundColor },
        style,
      ]}
      {...props}
    />
  );
}

