import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

import { authStorage, storage } from '@/utils/storage';

/**
 * Debug component to check storage state
 * Only use this for development/testing
 */
export function DebugStorage() {
  const [storageData, setStorageData] = useState<any>(null);
  const [hasSession, setHasSession] = useState<boolean>(false);

  const checkStorage = async () => {
    try {
      const session = await authStorage.loadSession();
      const hasActiveSession = await authStorage.hasActiveSession();
      const allKeys = await storage.getAllKeys();
      
      setStorageData({
        session,
        allKeys,
        timestamp: new Date().toISOString(),
      });
      setHasSession(hasActiveSession);
    } catch (error) {
      console.error('Failed to check storage:', error);
    }
  };

  const clearStorage = async () => {
    try {
      await authStorage.clearSession();
      await checkStorage();
    } catch (error) {
      console.error('Failed to clear storage:', error);
    }
  };

  useEffect(() => {
    checkStorage();
  }, []);

  if (__DEV__) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Debug Storage</Text>
        
        <View style={styles.row}>
          <TouchableOpacity style={styles.button} onPress={checkStorage}>
            <Text style={styles.buttonText}>Refresh</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.button, styles.clearButton]} onPress={clearStorage}>
            <Text style={styles.buttonText}>Clear</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.label}>Has Active Session: {hasSession ? 'Yes' : 'No'}</Text>
        
        {storageData && (
          <View style={styles.dataContainer}>
            <Text style={styles.label}>Storage Data:</Text>
            <Text style={styles.data}>{JSON.stringify(storageData, null, 2)}</Text>
          </View>
        )}
      </View>
    );
  }

  return null;
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 100,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 10,
    borderRadius: 8,
    maxWidth: 300,
    zIndex: 1000,
  },
  title: {
    color: 'white',
    fontWeight: 'bold',
    marginBottom: 10,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  clearButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: 'white',
    fontSize: 12,
  },
  label: {
    color: 'white',
    fontSize: 12,
    marginBottom: 5,
  },
  dataContainer: {
    marginTop: 10,
  },
  data: {
    color: '#00FF00',
    fontSize: 10,
    fontFamily: 'monospace',
  },
});
