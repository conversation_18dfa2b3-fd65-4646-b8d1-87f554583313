import { useAuth } from '@/context/AuthContext';
import { useRouter, useSegments } from 'expo-router';
import { useEffect, useRef } from 'react';

/**
 * Component that guards routes based on authentication state
 * Redirects to login if not authenticated on protected routes
 */
export function AuthGuard({ children }: { children: React.ReactNode }) {
  const { user, isLoading } = useAuth();
  const segments = useSegments();
  const router = useRouter();

  // Track previous user state to detect changes
  const prevUserRef = useRef(user);
  const initialCheckDoneRef = useRef(false);

  useEffect(() => {
    // Skip when loading
    if (isLoading) return;

    // Check if user state changed or it's the initial check
    const userChanged = prevUserRef.current !== user;
    const isInitialCheck = !initialCheckDoneRef.current;

    console.log('AuthGuard: Effect triggered', {
      user: user ? user.name : 'null',
      userChanged,
      isInitialCheck,
      segments: segments.join('/'),
      isLoading
    });

    // Only proceed if user changed or it's the initial check
    if (!userChanged && !isInitialCheck) {
      console.log('AuthGuard: Skipping - no user change and not initial check');
      return;
    }

    // Update previous user reference
    prevUserRef.current = user;

    // Check if the path is in the (auth) group
    const inAuthGroup = segments[0] === '(auth)';

    console.log('AuthGuard: Processing auth check', {
      user: user ? 'logged in' : 'not logged in',
      inAuthGroup,
      segments: segments.join('/')
    });

    // If not logged in and not in auth group, redirect to login
    if (!user && !inAuthGroup) {
      console.log('AuthGuard: Redirecting to login - no user found');

      // Use a small timeout to ensure the navigation happens
      setTimeout(() => {
        console.log('AuthGuard: Executing navigation to login');
        try {
          router.push('/(auth)/login');
          console.log('AuthGuard: Navigation completed successfully');
        } catch (error) {
          console.error('AuthGuard: Navigation failed:', error);
          // Fallback to replace
          router.replace('/(auth)/login');
        }
      }, 100);
    }

    // If logged in and in auth group, redirect to dashboard
    else if (user && inAuthGroup) {
      console.log('AuthGuard: Redirecting to dashboard - user found in auth group');
      router.replace('/(tabs)');
    }

    // Mark initial check as done
    if (isInitialCheck) {
      initialCheckDoneRef.current = true;
    }
  }, [user, isLoading]); // Depend on user and loading state

  // Show children
  return <>{children}</>;
}
