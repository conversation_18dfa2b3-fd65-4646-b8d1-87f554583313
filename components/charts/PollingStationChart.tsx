import React from 'react';
import { Dimensions, StyleSheet, View } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';

const { width } = Dimensions.get('window');

type PollingStationChartProps = {
  data: {
    station: string;
    votes: number;
    target: number;
  }[];
  title?: string;
};

export default function PollingStationChart({ data, title }: PollingStationChartProps) {
  return (
    <ThemedView style={styles.container}>
      {title && <ThemedText style={styles.title}>{title}</ThemedText>}

      <View style={styles.chartContainer}>
        {data.map((item, index) => {
          const percentage = Math.round((item.votes / item.target) * 100);
          let barColor = '#EF4444'; // Red (default)

          if (percentage >= 90) barColor = '#10B981'; // Green
          else if (percentage >= 70) barColor = '#3B82F6'; // Blue
          else if (percentage >= 50) barColor = '#F59E0B'; // Orange

          return (
            <View key={index} style={styles.barItem}>
              <View style={styles.barLabelContainer}>
                <ThemedText style={styles.barLabel} numberOfLines={1} ellipsizeMode="tail">
                  {item.station}
                </ThemedText>
                <ThemedText style={styles.barValues}>
                  {item.votes.toLocaleString()} / {item.target.toLocaleString()}
                </ThemedText>
              </View>

              <View style={styles.barContainer}>
                <View
                  style={[
                    styles.bar,
                    {
                      width: `${Math.min(100, percentage)}%`,
                      backgroundColor: barColor
                    }
                  ]}
                />
                <ThemedText style={[styles.percentageLabel, { color: barColor }]}>
                  {percentage}%
                </ThemedText>
              </View>
            </View>
          );
        })}
      </View>

      <View style={styles.legendContainer}>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: '#10B981' }]} />
          <ThemedText style={styles.legendText}>90-100% (Excellent)</ThemedText>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: '#3B82F6' }]} />
          <ThemedText style={styles.legendText}>70-89% (Good)</ThemedText>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: '#F59E0B' }]} />
          <ThemedText style={styles.legendText}>50-69% (Average)</ThemedText>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: '#EF4444' }]} />
          <ThemedText style={styles.legendText}>Below 50% (Poor)</ThemedText>
        </View>
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.neutral[800],
    marginBottom: 16,
  },
  chartContainer: {
    marginBottom: 16,
  },
  barItem: {
    marginBottom: 16,
  },
  barLabelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  barLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.neutral[800],
    flex: 1,
    marginRight: 8,
  },
  barValues: {
    fontSize: 12,
    color: Colors.light.neutral[500],
  },
  barContainer: {
    height: 16,
    backgroundColor: Colors.light.neutral[100],
    borderRadius: 8,
    overflow: 'hidden',
    flexDirection: 'row',
    alignItems: 'center',
  },
  bar: {
    height: '100%',
    borderRadius: 8,
  },
  percentageLabel: {
    position: 'absolute',
    right: 8,
    fontSize: 12,
    fontWeight: 'bold',
  },
  legendContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 8,
    paddingHorizontal: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '48%',
    marginBottom: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
    color: Colors.light.neutral[600],
  },
});
