import React from 'react';
import { Dimensions, StyleSheet, View } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';

const { width } = Dimensions.get('window');

type VoteDistributionChartProps = {
  data: {
    party: string;
    votes: number;
    color: string;
  }[];
  title?: string;
};

export default function VoteDistributionChart({ data, title }: VoteDistributionChartProps) {
  // Calculate total votes
  const totalVotes = data.reduce((sum, item) => sum + item.votes, 0);

  return (
    <ThemedView style={styles.container}>
      {title && <ThemedText style={styles.title}>{title}</ThemedText>}

      <View style={styles.chartContainer}>
        <View style={styles.totalVotesContainer}>
          <ThemedText style={styles.totalVotesNumber}>
            {totalVotes.toLocaleString()}
          </ThemedText>
          <ThemedText style={styles.totalVotesLabel}>
            Total Votes
          </ThemedText>
        </View>
      </View>

      <View style={styles.legendContainer}>
        {data.map((item, index) => {
          const percentage = Math.round((item.votes / totalVotes) * 100);
          return (
            <View key={index} style={styles.legendItem}>
              <View style={[styles.legendColorBox, { backgroundColor: item.color }]} />
              <View style={styles.legendTextContainer}>
                <ThemedText style={styles.legendParty}>
                  {item.party} ({percentage}%)
                </ThemedText>
                <ThemedText style={styles.legendVotes}>
                  {item.votes.toLocaleString()} votes
                </ThemedText>
              </View>
              <View style={styles.percentageBarContainer}>
                <View
                  style={[
                    styles.percentageBar,
                    {
                      width: `${percentage}%`,
                      backgroundColor: item.color
                    }
                  ]}
                />
              </View>
            </View>
          );
        })}
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.neutral[800],
    marginBottom: 16,
  },
  chartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 120,
    marginBottom: 20,
  },
  totalVotesContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 140,
    height: 140,
    borderRadius: 70,
    borderWidth: 8,
    borderColor: Colors.light.primary,
    backgroundColor: 'white',
  },
  totalVotesNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.neutral[800],
  },
  totalVotesLabel: {
    fontSize: 14,
    color: Colors.light.neutral[500],
  },
  legendContainer: {
    marginTop: 16,
    paddingHorizontal: 8,
  },
  legendItem: {
    marginBottom: 16,
  },
  legendColorBox: {
    width: 16,
    height: 16,
    borderRadius: 4,
    marginRight: 8,
    position: 'absolute',
  },
  legendTextContainer: {
    marginLeft: 28,
    marginBottom: 6,
  },
  legendParty: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.neutral[800],
  },
  legendVotes: {
    fontSize: 12,
    color: Colors.light.neutral[500],
  },
  percentageBarContainer: {
    height: 8,
    backgroundColor: Colors.light.neutral[100],
    borderRadius: 4,
    overflow: 'hidden',
  },
  percentageBar: {
    height: '100%',
    borderRadius: 4,
  },
});
