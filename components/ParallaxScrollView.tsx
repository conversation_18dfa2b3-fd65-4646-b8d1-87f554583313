
import { useColorScheme } from '@/hooks/useColorScheme';
import React, { ReactNode } from 'react';
import {
    Animated,
    Platform,
    SafeAreaView,
    StatusBar,
    StyleSheet,
    View,
    ViewStyle
} from 'react-native';

interface ParallaxScrollViewProps {
  children: ReactNode;
  headerBackgroundColor?: {
    light: string;
    dark: string;
  };
  headerImage?: ReactNode;
  headerHeight?: number;
  contentPadding?: number;
  style?: ViewStyle;
}

export default function ParallaxScrollView({
  children,
  headerBackgroundColor = { light: '#f5f5f5', dark: '#1c1c1e' },
  headerImage,
  headerHeight = 200,
  contentPadding = 16,
  style,
}: ParallaxScrollViewProps) {
  const colorScheme = useColorScheme();
  // Use useRef to persist the Animated.Value across renders
  const scrollY = React.useRef(new Animated.Value(0)).current;

  const headerBackgroundColorValue =
    colorScheme === 'dark' ? headerBackgroundColor.dark : headerBackgroundColor.light;

  const headerTranslate = scrollY.interpolate({
    inputRange: [0, headerHeight],
    outputRange: [0, -headerHeight / 2],
    extrapolate: 'clamp',
  });

  const imageOpacity = scrollY.interpolate({
    inputRange: [0, headerHeight / 2, headerHeight],
    outputRange: [1, 0.5, 0],
    extrapolate: 'clamp',
  });

  const statusBarHeight = Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;

  return (
    <View style={[styles.container, style]}>
      <Animated.View
        style={[
          styles.header,
          {
            backgroundColor: headerBackgroundColorValue,
            height: headerHeight,
            transform: [{ translateY: headerTranslate }],
          },
        ]}>
        <Animated.View style={[styles.headerImageContainer, { opacity: imageOpacity }]}>
          {headerImage}
        </Animated.View>
      </Animated.View>

      <Animated.ScrollView
        scrollEventThrottle={16}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: true }
        )}
        contentContainerStyle={{
          paddingTop: headerHeight + statusBarHeight,
          paddingHorizontal: contentPadding,
          paddingBottom: 30,
        }}>
        <SafeAreaView>
          {children}
        </SafeAreaView>
      </Animated.ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    overflow: 'hidden',
    zIndex: 10,
  },
  headerImageContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

