
import React from 'react';
import { Text, TextProps, StyleSheet } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';

type TextType = 'default' | 'defaultSemiBold' | 'title' | 'subtitle' | 'link';

interface ThemedTextProps extends TextProps {
  type?: TextType;
  lightColor?: string;
  darkColor?: string;
}

export function ThemedText({
  style,
  lightColor,
  darkColor,
  type = 'default',
  ...props
}: ThemedTextProps) {
  const colorScheme = useColorScheme();
  
  const color = colorScheme === 'dark'
    ? darkColor || Colors.dark.text
    : lightColor || Colors.light.text;

  return (
    <Text
      style={[
        styles[type],
        { color },
        style,
      ]}
      {...props}
    />
  );
}

const styles = StyleSheet.create({
  default: {
    fontSize: 16,
  },
  defaultSemiBold: {
    fontSize: 16,
    fontWeight: '600',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  link: {
    fontSize: 16,
    color: '#2196F3',
  },
});

