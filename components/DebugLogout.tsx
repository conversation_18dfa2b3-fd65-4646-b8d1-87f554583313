import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { useRouter, useSegments } from 'expo-router';

import { useAuth } from '@/context/AuthContext';
import { Colors } from '@/constants/Colors';

/**
 * Debug component to test logout functionality
 * Only use this for development/testing
 */
export function DebugLogout() {
  const { user, logout, isLoading } = useAuth();
  const router = useRouter();
  const segments = useSegments();
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    setDebugInfo({
      user: user ? { name: user.name, role: user.role } : null,
      isLoading,
      segments: segments.join('/'),
      timestamp: new Date().toLocaleTimeString(),
    });
  }, [user, isLoading, segments]);

  const handleDirectLogout = async () => {
    try {
      console.log('DebugLogout: Starting direct logout');
      await logout();
      console.log('DebugLogout: Logout completed');
    } catch (error) {
      console.error('DebugLogout: Logout failed', error);
    }
  };

  const handleDirectNavigation = () => {
    console.log('DebugLogout: Direct navigation to login');
    router.replace('/(auth)/login');
  };

  const testLogout = () => {
    Alert.alert(
      "Debug Logout",
      "Choose logout method:",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Direct Logout",
          onPress: handleDirectLogout,
        },
        {
          text: "Direct Navigation",
          onPress: handleDirectNavigation,
        }
      ]
    );
  };

  if (__DEV__ && user) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Debug Logout</Text>
        
        <View style={styles.infoContainer}>
          <Text style={styles.infoText}>User: {debugInfo.user?.name}</Text>
          <Text style={styles.infoText}>Role: {debugInfo.user?.role}</Text>
          <Text style={styles.infoText}>Loading: {debugInfo.isLoading ? 'Yes' : 'No'}</Text>
          <Text style={styles.infoText}>Segments: {debugInfo.segments}</Text>
          <Text style={styles.infoText}>Updated: {debugInfo.timestamp}</Text>
        </View>
        
        <TouchableOpacity style={styles.testButton} onPress={testLogout}>
          <Text style={styles.testButtonText}>Test Logout</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return null;
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 300,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    padding: 15,
    borderRadius: 8,
    maxWidth: 250,
    zIndex: 1000,
  },
  title: {
    color: 'white',
    fontWeight: 'bold',
    marginBottom: 10,
    fontSize: 14,
  },
  infoContainer: {
    marginBottom: 15,
  },
  infoText: {
    color: '#00FF00',
    fontSize: 10,
    fontFamily: 'monospace',
    marginBottom: 2,
  },
  testButton: {
    backgroundColor: Colors.light.error,
    padding: 10,
    borderRadius: 6,
  },
  testButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
});
