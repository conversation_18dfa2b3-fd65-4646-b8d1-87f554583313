import React from "react";
import { StyleSheet, View } from "react-native";
import { ThemedText } from "@/components/ThemedText";

interface LogoBoxProps {
  size?: number;
  borderRadius?: number;
  backgroundColor?: string;
  textColor?: string;
  fontSize?: number;
  letter?: string;
  shadow?: boolean;
}

export default function LogoBox({
  size = 80,
  borderRadius = 20,
  backgroundColor = "white",
  textColor = "#2563EB",
  fontSize = 36,
  letter = "M",
  shadow = true,
}: LogoBoxProps) {
  return (
    <View 
      style={[
        styles.logo, 
        { 
          width: size, 
          height: size, 
          borderRadius: borderRadius,
          backgroundColor: backgroundColor,
          ...(!shadow ? {} : styles.shadow)
        }
      ]}
    >
      <ThemedText style={[styles.logoText, { fontSize, color: textColor }]}>
        {letter}
      </ThemedText>
    </View>
  );
}

const styles = StyleSheet.create({
  logo: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  shadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  logoText: {
    fontWeight: 'bold',
  },
});