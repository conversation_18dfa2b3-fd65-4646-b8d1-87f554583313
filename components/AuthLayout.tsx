import React from "react";
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  View
} from "react-native";

import { ThemedView } from "@/components/ThemedView";

const { width, height } = Dimensions.get('window');

interface AuthLayoutProps {
  children: React.ReactNode;
  headerBackgroundColor?: string;
  statusBarColor?: string;
  statusBarStyle?: "light-content" | "dark-content";
  contentMaxWidth?: number;
}

export default function AuthLayout({
  children,
  headerBackgroundColor = "#2563EB",
  statusBarColor = "#2563EB",
  statusBarStyle = "light-content",
  contentMaxWidth = 400,
}: AuthLayoutProps) {
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardView}
    >
      <StatusBar barStyle={statusBarStyle} backgroundColor={statusBarColor} />
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollView}
      >
        <ThemedView style={styles.container}>
          <View 
            style={[
              styles.headerBackground, 
              { backgroundColor: headerBackgroundColor }
            ]} 
          />
          
          <View style={[
            styles.contentWrapper,
            { maxWidth: contentMaxWidth }
          ]}>
            {children}
          </View>
        </ThemedView>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  keyboardView: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollView: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    minHeight: height,
    position: 'relative',
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: height * 0.25,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    zIndex: 0,
  },
  contentWrapper: {
    width: '100%',
    alignSelf: 'center',
    paddingHorizontal: 24,
    paddingVertical: 40,
    zIndex: 1,
  },
});