import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Alert } from 'react-native';

import { useAuth } from '@/context/AuthContext';
import { Colors } from '@/constants/Colors';

/**
 * Simple logout test component for development
 * Only use this for testing logout functionality
 */
export function LogoutTest() {
  const { logout, user } = useAuth();

  const handleLogout = () => {
    Alert.alert(
      "Test Logout",
      "Are you sure you want to logout?",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Logout",
          onPress: logout,
          style: "destructive"
        }
      ]
    );
  };

  if (__DEV__ && user) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Logout Test</Text>
        <Text style={styles.userInfo}>User: {user.name}</Text>
        <Text style={styles.userInfo}>Role: {user.role}</Text>
        
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Text style={styles.logoutText}>Test Logout</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return null;
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 200,
    right: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 15,
    borderRadius: 8,
    maxWidth: 200,
    zIndex: 1000,
    borderWidth: 1,
    borderColor: Colors.light.neutral[200],
    shadowColor: Colors.light.neutral[900],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 10,
    color: Colors.light.neutral[800],
  },
  userInfo: {
    fontSize: 12,
    color: Colors.light.neutral[600],
    marginBottom: 5,
  },
  logoutButton: {
    backgroundColor: Colors.light.error,
    padding: 10,
    borderRadius: 6,
    marginTop: 10,
  },
  logoutText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
});
