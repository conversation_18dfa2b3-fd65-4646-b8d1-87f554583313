
import React from 'react';
import { TouchableOpacity, Platform } from 'react-native';
import * as Haptics from 'expo-haptics';

/**
 * A tab button component that provides haptic feedback when pressed
 */
export function HapticTab({ onPress, ...props }) {
  const handlePress = () => {
    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    onPress && onPress();
  };

  return <TouchableOpacity {...props} onPress={handlePress} />;
}

