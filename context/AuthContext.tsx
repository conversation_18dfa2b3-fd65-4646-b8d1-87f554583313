import { router } from 'expo-router';
import React, { createContext, useContext, useEffect, useState } from 'react';

import { authStorage } from '@/utils/storage';



// Define user roles
export type UserRole = 'hon' | 'manager' | 'agent';

// Define user interface
export interface User {
  id: string;
  name: string;
  phone: string;
  role: UserRole;
  station?: string; // Only for agents
}

// Define auth context interface
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (phone: string) => Promise<void>;
  register: (name: string, phone: string, role: UserRole, station?: string) => Promise<void>;
  logout: () => void;
}

// Sample users for demo
const SAMPLE_USERS: User[] = [
  {
    id: '1',
    name: 'Hon. <PERSON>',
    phone: '1111', // Simplified for easy testing
    role: 'hon',
  },
  {
    id: '2',
    name: '<PERSON>',
    phone: '2222', // Simplified for easy testing
    role: 'manager',
  },
  {
    id: '3',
    name: '<PERSON>',
    phone: '3333', // Simplified for easy testing
    role: 'agent',
    station: 'Kampala Central A',
  },
];

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Storage helper functions using our utility
  const saveUserToStorage = async (userData: User) => {
    await authStorage.saveSession(userData);
  };

  const removeUserFromStorage = async () => {
    await authStorage.clearSession();
  };

  const loadUserFromStorage = async (): Promise<User | null> => {
    return await authStorage.loadSession();
  };

  // Check for existing session on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Load user from storage
        const storedUser = await loadUserFromStorage();

        if (storedUser) {
          setUser(storedUser);
        }
      } catch (error) {
        console.error('Auth check failed:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Login function
  const login = async (phone: string) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Find user by phone
      const foundUser = SAMPLE_USERS.find(u => u.phone === phone);

      if (foundUser) {
        // Set user first, then save to storage
        setUser(foundUser);
        await saveUserToStorage(foundUser);

        // Use a small timeout to ensure state is updated before navigation
        setTimeout(() => {
          router.replace('/(tabs)');
        }, 100);
      } else {
        throw new Error('User not found');
      }
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    console.log('AuthContext: Starting logout process', { currentUser: user?.name });

    // Clear user data from state first - this should trigger AuthGuard
    setUser(null);

    // Clear storage in background
    try {
      await removeUserFromStorage();
      console.log('AuthContext: Storage cleared successfully');
    } catch (error) {
      console.error('AuthContext: Failed to clear storage:', error);
    }

    // Force navigation as backup
    setTimeout(() => {
      console.log('AuthContext: Force navigation to login as backup');
      try {
        router.push('/(auth)/login');
        console.log('AuthContext: Backup navigation completed');
      } catch (error) {
        console.error('AuthContext: Backup navigation failed:', error);
        router.replace('/(auth)/login');
      }
    }, 200);

    console.log('AuthContext: Logout completed - user state cleared');
  };

  // Register function
  const register = async (name: string, phone: string, role: UserRole, station?: string) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Create new user
      const newUser: User = {
        id: Date.now().toString(),
        name,
        phone,
        role,
        station,
      };

      // Auto-login
      setUser(newUser);
      await saveUserToStorage(newUser);

      // Use a small timeout to ensure state is updated before navigation
      setTimeout(() => {
        router.replace('/(tabs)');
      }, 100);
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    login,
    register,
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
