import React, { createContext, useContext, useEffect, useState } from 'react';

import { storage } from '@/utils/storage';

// DR Form status types
export type DrFormStatus = 0 | 1 | 2 | 3; // 0: pending, 1: captured, 2: uploaded, 3: verified

// DR Form data interface
export interface DrFormData {
  id: string;
  status: DrFormStatus;
  imageUrl: string;
  statusDescription: string;
  submittedAt?: string;
  verifiedAt?: string;
  agentId: string;
  stationId?: string;
}

// DR Form context interface
interface DrFormContextType {
  drFormData: DrFormData | null;
  isLoading: boolean;
  updateDrFormStatus: (status: DrFormStatus) => Promise<void>;
  updateDrFormImage: (imageUrl: string) => Promise<void>;
  updateDrFormDescription: (description: string) => Promise<void>;
  submitDrForm: (imageUrl: string, description: string) => Promise<void>;
  resetDrForm: () => Promise<void>;
  getDrFormProgress: () => number;
  getDrFormStatusText: () => string;
  getDrFormStatusIcon: () => string;
  initializeDrForm: (agentId: string, stationId?: string) => void;
}

// Create context
const DrFormContext = createContext<DrFormContextType | undefined>(undefined);

// Storage key for DR form data
const DR_FORM_STORAGE_KEY = '@mitally_dr_form';

// DR Form provider component
export function DrFormProvider({ children }: { children: React.ReactNode }) {
  const [drFormData, setDrFormData] = useState<DrFormData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load DR form data from storage on mount
  useEffect(() => {
    loadDrFormFromStorage();
  }, []);

  // Load DR form data from storage
  const loadDrFormFromStorage = async () => {
    try {
      const savedData = await storage.getItem<DrFormData>(DR_FORM_STORAGE_KEY);
      if (savedData) {
        setDrFormData(savedData);
      }
    } catch (error) {
      console.error('Failed to load DR form data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Save DR form data to storage
  const saveDrFormToStorage = async (data: DrFormData) => {
    try {
      await storage.setItem(DR_FORM_STORAGE_KEY, data);
    } catch (error) {
      console.error('Failed to save DR form data:', error);
    }
  };

  // Create initial DR form data
  const createInitialDrForm = (agentId: string, stationId?: string): DrFormData => {
    return {
      id: Date.now().toString(),
      status: 0,
      imageUrl: '',
      statusDescription: '',
      agentId,
      stationId,
    };
  };

  // Update DR form status
  const updateDrFormStatus = async (status: DrFormStatus) => {
    if (!drFormData) return;

    const updatedData: DrFormData = {
      ...drFormData,
      status,
      ...(status === 2 && { submittedAt: new Date().toISOString() }),
      ...(status === 3 && { verifiedAt: new Date().toISOString() }),
    };

    setDrFormData(updatedData);
    await saveDrFormToStorage(updatedData);
  };

  // Update DR form image
  const updateDrFormImage = async (imageUrl: string) => {
    if (!drFormData) return;

    const updatedData: DrFormData = {
      ...drFormData,
      imageUrl,
      status: imageUrl ? Math.max(drFormData.status, 1) as DrFormStatus : drFormData.status,
    };

    setDrFormData(updatedData);
    await saveDrFormToStorage(updatedData);
  };

  // Update DR form description
  const updateDrFormDescription = async (description: string) => {
    if (!drFormData) return;

    const updatedData: DrFormData = {
      ...drFormData,
      statusDescription: description,
    };

    setDrFormData(updatedData);
    await saveDrFormToStorage(updatedData);
  };

  // Submit DR form
  const submitDrForm = async (imageUrl: string, description: string) => {
    if (!drFormData) return;

    const updatedData: DrFormData = {
      ...drFormData,
      imageUrl,
      statusDescription: description,
      status: 2, // Uploaded
      submittedAt: new Date().toISOString(),
    };

    setDrFormData(updatedData);
    await saveDrFormToStorage(updatedData);

    // Simulate verification after 3 seconds
    setTimeout(async () => {
      const verifiedData: DrFormData = {
        ...updatedData,
        status: 3, // Verified
        verifiedAt: new Date().toISOString(),
      };

      setDrFormData(verifiedData);
      await saveDrFormToStorage(verifiedData);
    }, 3000);
  };

  // Reset DR form
  const resetDrForm = async () => {
    try {
      await storage.removeItem(DR_FORM_STORAGE_KEY);
      setDrFormData(null);
    } catch (error) {
      console.error('Failed to reset DR form:', error);
    }
  };

  // Get DR form progress percentage
  const getDrFormProgress = (): number => {
    if (!drFormData) return 0;

    switch (drFormData.status) {
      case 0: return 0;
      case 1: return 33;
      case 2: return 66;
      case 3: return 100;
      default: return 0;
    }
  };

  // Get DR form status text
  const getDrFormStatusText = (): string => {
    if (!drFormData) return "Pending";

    switch (drFormData.status) {
      case 0: return "Pending";
      case 1: return "Captured";
      case 2: return "Uploaded";
      case 3: return "Verified";
      default: return "Pending";
    }
  };

  // Get DR form status icon
  const getDrFormStatusIcon = (): string => {
    if (!drFormData) return "clock.fill";

    switch (drFormData.status) {
      case 0: return "clock.fill";
      case 1: return "camera.fill";
      case 2: return "arrow.up.circle.fill";
      case 3: return "checkmark.circle.fill";
      default: return "clock.fill";
    }
  };

  // Initialize DR form for new users
  const initializeDrForm = (agentId: string, stationId?: string) => {
    if (!drFormData && agentId) {
      const initialData = createInitialDrForm(agentId, stationId);
      setDrFormData(initialData);
      saveDrFormToStorage(initialData);
    }
  };

  const value: DrFormContextType = {
    drFormData,
    isLoading,
    updateDrFormStatus,
    updateDrFormImage,
    updateDrFormDescription,
    submitDrForm,
    resetDrForm,
    getDrFormProgress,
    getDrFormStatusText,
    getDrFormStatusIcon,
    initializeDrForm,
  };

  return (
    <DrFormContext.Provider value={value}>
      {children}
    </DrFormContext.Provider>
  );
}

// Hook to use DR form context
export function useDrForm() {
  const context = useContext(DrFormContext);
  if (context === undefined) {
    throw new Error('useDrForm must be used within a DrFormProvider');
  }
  return context;
}


